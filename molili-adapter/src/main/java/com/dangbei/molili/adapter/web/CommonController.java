package com.dangbei.molili.adapter.web;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 通用控制器
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-11
 */
@Slf4j
@Tag(name = "CommonController", description = "通用服务")
@RestController
@RequestMapping("/common/v1")
public class CommonController {

    /**
     * 验证码缓存前缀
     */
    private static final String VERIFICATION_CODE_PREFIX = "verification_code:";
    
    /**
     * 验证码有效期（分钟）
     */
    private static final int VERIFICATION_CODE_EXPIRE_MINUTES = 5;
    
    /**
     * 验证码长度
     */
    private static final int VERIFICATION_CODE_LENGTH = 6;

    @Operation(summary = "发送邮箱验证码")
    @PostMapping("/send-verification-code")
    public Response sendVerificationCode(@Valid @RequestBody SendVerificationCodeRequest request) {
        log.info("发送邮箱验证码请求: {}", request.getEmail());
        
        try {
            // 参数验证
            if (StrUtil.isBlank(request.getEmail())) {
                return Response.buildFailure("400", "邮箱地址不能为空");
            }
            
            // 生成6位数字验证码
            String verificationCode = RandomUtil.randomNumbers(VERIFICATION_CODE_LENGTH);
            
            // 将验证码存储到Redis，设置5分钟过期
            String cacheKey = VERIFICATION_CODE_PREFIX + request.getEmail();
            RedisUtil.set(cacheKey, verificationCode, VERIFICATION_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // TODO: 这里应该调用邮件服务发送验证码
            // 目前先记录日志，实际项目中需要集成邮件服务
            log.info("验证码已生成并缓存，邮箱: {}, 验证码: {}", request.getEmail(), verificationCode);
            
            // 模拟邮件发送成功
            log.info("邮箱验证码发送成功，邮箱: {}", request.getEmail());
            
            return Response.buildSuccess();
            
        } catch (Exception e) {
            log.error("发送邮箱验证码失败，邮箱: {}", request.getEmail(), e);
            return Response.buildFailure("500", "发送验证码失败，系统错误");
        }
    }

    @Operation(summary = "验证邮箱验证码")
    @PostMapping("/verify-code")
    public SingleResponse<Boolean> verifyCode(@Valid @RequestBody VerifyCodeRequest request) {
        log.info("验证邮箱验证码请求: {}", request.getEmail());
        
        try {
            // 参数验证
            if (StrUtil.isBlank(request.getEmail())) {
                return SingleResponse.buildFailure("400", "邮箱地址不能为空");
            }
            
            if (StrUtil.isBlank(request.getCode())) {
                return SingleResponse.buildFailure("400", "验证码不能为空");
            }
            
            // 从Redis获取验证码
            String cacheKey = VERIFICATION_CODE_PREFIX + request.getEmail();
            String cachedCode = RedisUtil.get(cacheKey);
            
            if (Objects.isNull(cachedCode)) {
                log.warn("验证码不存在或已过期，邮箱: {}", request.getEmail());
                return SingleResponse.buildFailure("400", "验证码不存在或已过期");
            }
            
            // 验证码比较
            boolean isValid = Objects.equals(cachedCode, request.getCode());
            
            if (isValid) {
                // 验证成功，删除验证码
                RedisUtil.del(cacheKey);
                log.info("邮箱验证码验证成功，邮箱: {}", request.getEmail());
                return SingleResponse.of(true);
            } else {
                log.warn("邮箱验证码验证失败，邮箱: {}, 输入验证码: {}", request.getEmail(), request.getCode());
                return SingleResponse.buildFailure("400", "验证码错误");
            }
            
        } catch (Exception e) {
            log.error("验证邮箱验证码失败，邮箱: {}", request.getEmail(), e);
            return SingleResponse.buildFailure("500", "验证码验证失败，系统错误");
        }
    }

    /**
     * 发送验证码请求对象
     */
    @Data
    @Schema(description = "发送验证码请求对象")
    public static class SendVerificationCodeRequest {

        @Schema(description = "邮箱地址", required = true)
        @NotBlank(message = "邮箱地址不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;
    }

    /**
     * 验证码验证请求对象
     */
    @Data
    @Schema(description = "验证码验证请求对象")
    public static class VerifyCodeRequest {

        @Schema(description = "邮箱地址", required = true)
        @NotBlank(message = "邮箱地址不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;

        @Schema(description = "验证码", required = true)
        @NotBlank(message = "验证码不能为空")
        private String code;
    }
}

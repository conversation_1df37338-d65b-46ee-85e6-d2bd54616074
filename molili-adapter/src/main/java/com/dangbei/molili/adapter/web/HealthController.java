package com.dangbei.molili.adapter.web;

import com.alibaba.cola.dto.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "HealthController", description = "健康检查服务")
@RestController
@RequestMapping("/healthApi/v1")
public class HealthController {

    @Operation(summary = "存活探针")
    @GetMapping("/liveness")
    public Response liveness() {
        return Response.buildSuccess();
    }
}

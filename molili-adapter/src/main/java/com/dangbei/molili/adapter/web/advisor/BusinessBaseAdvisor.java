package com.dangbei.molili.adapter.web.advisor;

import com.alibaba.cola.common.constant.RequestConstant;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.BaseAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import reactor.core.publisher.Flux;

/**
 * 通用业务逻辑增强基类
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
public abstract class BusinessBaseAdvisor implements BaseAdvisor {

    String requestId = MDC.get(RequestConstant.REQUEST_ID);

    @NotNull
    @Override
    public ChatClientResponse adviseCall(@NotNull ChatClientRequest chatClientRequest, @NotNull CallAdvisorChain callAdvisorChain) {
        MDC.put(RequestConstant.REQUEST_ID, requestId);
        return BaseAdvisor.super.adviseCall(chatClientRequest, callAdvisorChain);
    }

    @NotNull
    @Override
    public Flux<ChatClientResponse> adviseStream(@NotNull ChatClientRequest chatClientRequest, @NotNull StreamAdvisorChain streamAdvisorChain) {
        MDC.put(RequestConstant.REQUEST_ID, requestId);
        return BaseAdvisor.super.adviseStream(chatClientRequest, streamAdvisorChain);
    }

}

package com.dangbei.molili.adapter.web;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.dangbei.molili.domain.entity.UserInfoEntity;
import com.dangbei.molili.domain.gateway.UserInfoGateway;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 登录控制器
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-11
 */
@Slf4j
@Tag(name = "LoginController", description = "用户登录服务")
@RestController
@RequestMapping("/login/v1")
public class LoginController {

    /**
     * 验证码缓存前缀
     */
    private static final String VERIFICATION_CODE_PREFIX = "verification_code:";
    
    /**
     * Cookie名称
     */
    private static final String COOKIE_NAME = "molili_username";
    private static final String USER_ID_COOKIE_NAME = "userId";
    
    /**
     * Cookie过期时间（秒）- 30天
     */
    private static final int COOKIE_MAX_AGE = 30 * 24 * 60 * 60;

    private final UserInfoGateway userInfoGateway;

    public LoginController(UserInfoGateway userInfoGateway) {
        this.userInfoGateway = userInfoGateway;
    }

    @Operation(summary = "邮箱验证码登录")
    @PostMapping("/email-login")
    public SingleResponse<LoginResult> emailLogin(@Valid @RequestBody EmailLoginRequest request, 
                                                  HttpServletResponse response) {
        log.info("邮箱验证码登录请求: {}", request.getEmail());
        
        try {
            // 参数验证
            if (StrUtil.isBlank(request.getEmail())) {
                return SingleResponse.buildFailure("400", "邮箱地址不能为空");
            }
            
            if (StrUtil.isBlank(request.getCode())) {
                return SingleResponse.buildFailure("400", "验证码不能为空");
            }
            
            // 验证验证码
            String cacheKey = VERIFICATION_CODE_PREFIX + request.getEmail();
            String cachedCode = RedisUtil.get(cacheKey);
            
            if (Objects.isNull(cachedCode)) {
                log.warn("验证码不存在或已过期，邮箱: {}", request.getEmail());
                return SingleResponse.buildFailure("400", "验证码不存在或已过期");
            }
            
            if (!Objects.equals(cachedCode, request.getCode())) {
                log.warn("验证码错误，邮箱: {}, 输入验证码: {}", request.getEmail(), request.getCode());
                return SingleResponse.buildFailure("400", "验证码错误");
            }
            
            // 验证码验证成功，删除验证码
            RedisUtil.del(cacheKey);
            
            // 查询或创建用户
            UserInfoEntity userInfo = userInfoGateway.findByUserId(request.getEmail());
            
            if (Objects.isNull(userInfo)) {
                // 用户不存在，创建新用户
                userInfo = new UserInfoEntity();
                userInfo.setUserId(request.getEmail());
                userInfo.setUsername(request.getEmail());
                userInfo.setEmail(request.getEmail());
                userInfo.setCreatedAt(LocalDateTime.now());
                userInfo.setLastLoginAt(LocalDateTime.now());
                userInfo.setBanned(0); // 0表示未封禁
                
                // 保存用户信息
                Long userId = userInfoGateway.insert(userInfo);
                log.info("创建新用户成功，邮箱: {}, 数据库ID: {}", request.getEmail(), userId);
                
            } else {
                // 用户已存在，更新最后登录时间和邮箱信息
                userInfo.setLastLoginAt(LocalDateTime.now());
                if (StrUtil.isBlank(userInfo.getEmail())) {
                    userInfo.setEmail(request.getEmail());
                }
                userInfoGateway.update(userInfo);
                log.info("用户登录成功，邮箱: {}", request.getEmail());
            }
            
            // 设置Cookie
            setCookie(response, COOKIE_NAME, request.getEmail());
            setCookie(response, USER_ID_COOKIE_NAME, request.getEmail());
            
            // 构建登录结果
            LoginResult loginResult = new LoginResult();
            loginResult.setUserId(userInfo.getUserId());
            loginResult.setUsername(userInfo.getUsername());
            loginResult.setEmail(userInfo.getEmail());
            loginResult.setAvatarUrl(userInfo.getAvatarUrl());
            
            log.info("邮箱验证码登录成功，邮箱: {}", request.getEmail());
            return SingleResponse.of(loginResult);
            
        } catch (Exception e) {
            log.error("邮箱验证码登录失败，邮箱: {}", request.getEmail(), e);
            return SingleResponse.buildFailure("500", "登录失败，系统错误");
        }
    }

    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public Response logout(HttpServletResponse response) {
        log.info("用户退出登录");
        
        try {
            // 清除Cookie
            clearCookie(response, COOKIE_NAME);
            clearCookie(response, USER_ID_COOKIE_NAME);
            
            log.info("用户退出登录成功");
            return Response.buildSuccess();
            
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return Response.buildFailure("500", "退出登录失败，系统错误");
        }
    }

    /**
     * 设置Cookie
     */
    private void setCookie(HttpServletResponse response, String name, String value) {
        Cookie cookie = new Cookie(name, value);
        cookie.setPath("/");
        cookie.setMaxAge(COOKIE_MAX_AGE);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
    }

    /**
     * 清除Cookie
     */
    private void clearCookie(HttpServletResponse response, String name) {
        Cookie cookie = new Cookie(name, "");
        cookie.setPath("/");
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }

    /**
     * 邮箱登录请求对象
     */
    @Data
    @Schema(description = "邮箱登录请求对象")
    public static class EmailLoginRequest {

        @Schema(description = "邮箱地址", required = true)
        @NotBlank(message = "邮箱地址不能为空")
        @Email(message = "邮箱格式不正确")
        private String email;

        @Schema(description = "验证码", required = true)
        @NotBlank(message = "验证码不能为空")
        private String code;
    }

    /**
     * 登录结果对象
     */
    @Data
    @Schema(description = "登录结果对象")
    public static class LoginResult {

        @Schema(description = "用户ID")
        private String userId;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "邮箱地址")
        private String email;

        @Schema(description = "头像链接")
        private String avatarUrl;
    }
}

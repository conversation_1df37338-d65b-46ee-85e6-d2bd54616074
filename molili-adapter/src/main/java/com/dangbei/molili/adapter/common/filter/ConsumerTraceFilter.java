// package com.dangbei.molili.adapter.common.filter;
//
// import com.alibaba.cola.common.constant.RequestConstant;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.dubbo.common.constants.CommonConstants;
// import org.apache.dubbo.common.extension.Activate;
// import org.apache.dubbo.rpc.Filter;
// import org.apache.dubbo.rpc.Invocation;
// import org.apache.dubbo.rpc.Invoker;
// import org.apache.dubbo.rpc.Result;
// import org.apache.dubbo.rpc.RpcContext;
// import org.apache.dubbo.rpc.RpcContextAttachment;
// import org.apache.dubbo.rpc.RpcException;
// import org.slf4j.MDC;
//
// import java.util.UUID;
//
// /**
//  * 服务消费者-链路过滤器
//  * <AUTHOR>
//  * @version 1.0.0
//  * @since 2024-06-19
//  */
// @Slf4j
// @Activate(group = CommonConstants.CONSUMER)
// public class ConsumerTraceFilter implements Filter {
//
//     @Override
//     public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
//         RpcContextAttachment clientAttachment = RpcContext.getClientAttachment();
//         try {
//             // 传递 SLF4J 的 MDC，传递 requestId
//             String requestId = MDC.get(RequestConstant.REQUEST_ID);
//             // 如果 requestId 为空，则生成一个
//             if (StringUtils.isBlank(requestId)) {
//                 requestId = UUID.randomUUID().toString();
//             }
//             clientAttachment.setAttachment(RequestConstant.REQUEST_ID, requestId);
//         } catch (Throwable ex) {
//             log.warn("DUBBO消费者服务上下文传递异常", ex);
//         }
//         return invoker.invoke(invocation);
//     }
// }

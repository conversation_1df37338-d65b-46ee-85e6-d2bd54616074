package com.dangbei.molili.adapter.common.exception;

import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.common.enums.BasicErrorCode;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 全局异常处理
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 参数缺失字段正则
     */
    private static final Pattern MISSING_PARAM_PATTERN = Pattern.compile("Required request parameter '(.*?)'");

    /**
     * 通用异常
     */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Throwable.class)
    public Response handleException(Throwable th) {
        log.error(th.getMessage(), th);
        Response response = Response.buildFailure(BasicErrorCode.SYS_ERROR.getErrCode(), "系统异常");
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * hibernate-validator 参数验证异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BindException.class)
    public Response handleException(BindException ex) {
        List<String> errorMessageList = ex.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.toList());
        String errorMessage = StringUtils.join(errorMessageList, ",");
        log.debug("参数错误：{}", errorMessage);
        Response response = Response.buildFailure(BasicErrorCode.DATA_FORMAT_ERROR.getErrCode(), errorMessage);
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * 参数缺失
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Response handleException(MissingServletRequestParameterException ex) {
        log.debug("参数缺失", ex);
        String missingField = "";
        String errorMessage = ex.getMessage();
        if (StringUtils.isNotBlank(errorMessage)) {
            Matcher matcher = MISSING_PARAM_PATTERN.matcher(errorMessage);
            if (matcher.find()) {
                missingField = matcher.group(1);
            }
        }
        Response response = Response.buildFailure(BasicErrorCode.DATA_FORMAT_ERROR.getErrCode(), "请求参数缺失: " + missingField);
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * 请求方式错误
     */
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Response handleException(HttpRequestMethodNotSupportedException ex) {
        log.debug("Http请求方式错误", ex);
        Response response = Response.buildFailure(BasicErrorCode.HTTP_METHOD_ERROR.getErrCode(), "Http请求方式错误");
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * Body 参数格式异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Response handleException(HttpMessageNotReadableException ex) {
        log.debug("Body 参数格式异常", ex);
        Response response = Response.buildFailure(BasicErrorCode.DATA_FORMAT_ERROR.getErrCode(), "参数格式异常");
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * Params 参数格式异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Response handleException(MethodArgumentTypeMismatchException ex) {
        log.debug("Params 参数格式异常", ex);
        Response response = Response.buildFailure(BasicErrorCode.DATA_FORMAT_ERROR.getErrCode(), "参数格式异常");
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * COLA 业务异常
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BizException.class)
    public Response handleException(BizException ex) {
        log.warn(ex.getMessage(), ex);
        Response response = Response.buildFailure(StringUtils.defaultIfBlank(ex.getErrCode(), BasicErrorCode.DATA_FORMAT_ERROR.getErrCode()), ex.getMessage());
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }

    /**
     * COLA 系统异常
     */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(SysException.class)
    public Response handleException(SysException ex) {
        log.error(ex.getMessage(), ex);
        Response response = Response.buildFailure(StringUtils.defaultIfBlank(ex.getErrCode(), BasicErrorCode.SYS_ERROR.getErrCode()), ex.getMessage());
        response.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        return response;
    }
}

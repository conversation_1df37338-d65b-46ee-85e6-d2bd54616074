package com.dangbei.molili.adapter.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.common.util.BeanCopyUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.dangbei.molili.adapter.web.advisor.ChatRetrievalAugmentationAdvisor;
import com.dangbei.molili.app.executor.OssCmdExe;
import com.dangbei.molili.client.dto.ChatMsgExt;
import com.dangbei.molili.client.dto.clientobject.OssStsCo;
import com.dangbei.molili.domain.entity.AgentTemplateEntity;
import com.dangbei.molili.domain.entity.ChatMessageEntity;
import com.dangbei.molili.domain.entity.ToolAgentEntity;
import com.dangbei.molili.domain.entity.UserAgentEntity;
import com.dangbei.molili.domain.entity.UserInfoEntity;
import com.dangbei.molili.domain.gateway.AgentTemplateGateway;
import com.dangbei.molili.domain.gateway.ChatMessageGateway;
import com.dangbei.molili.domain.gateway.ToolAgentGateway;
import com.dangbei.molili.domain.gateway.UserAgentGateway;
import com.dangbei.molili.domain.gateway.UserInfoGateway;
import com.dangbei.molili.infrastructure.chatMemory.MessageWindowChatMemory;
import com.dangbei.molili.infrastructure.config.properties.VolcMemoryProperties;
import com.dangbei.molili.infrastructure.memory.VolcMemoryClient;
import com.dangbei.molili.infrastructure.memory.request.MemoryAddRequest;
import com.dangbei.molili.infrastructure.memory.request.MemorySearchRequest;
import com.dangbei.molili.infrastructure.memory.response.MemoryAddResponse;
import com.dangbei.molili.infrastructure.memory.response.MemorySearchResponse;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.MDC;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.dangbei.molili.infrastructure.chatMemory.MessageWindowChatMemory.DEFAULT_KEY_PREFIX;

/**
 * playground
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Slf4j
@RestController
@RequestMapping("/playground/v1")
@Tag(name = "PlaygroundController", description = "playground服务")
public class PlaygroundController {

    private final Snowflake snowflake;
    private final UserInfoGateway userInfoGateway;
    private final AgentTemplateGateway agentTemplateGateway;
    private final ChatMessageGateway chatMessageGateway;
    private final UserAgentGateway userAgentGateway;
    private final ToolAgentGateway toolAgentGateway;
    private final Executor asyncExecutor;
    private final VolcMemoryClient volcMemoryClient;
    private final VolcMemoryProperties volcMemoryProperties;
    private final MessageWindowChatMemory messageWindowChatMemory;
    private final OssCmdExe ossCmdExe;

    public PlaygroundController(Snowflake snowflake,
                                UserInfoGateway userInfoGateway,
                                AgentTemplateGateway agentTemplateGateway,
                                UserAgentGateway userAgentGateway,
                                OpenAiChatModel openAiChatModel,
                                @Qualifier("chatAsyncExecutor") Executor asyncExecutor,
                                VolcMemoryProperties volcMemoryProperties,
                                ToolAgentGateway toolAgentGateway,
                                ChatMessageGateway chatMessageGateway,
                                VolcMemoryClient volcMemoryClient,
                                OssCmdExe ossCmdExe) {
        this.snowflake = snowflake;
        this.userInfoGateway = userInfoGateway;
        this.agentTemplateGateway = agentTemplateGateway;
        this.userAgentGateway = userAgentGateway;
        this.asyncExecutor = asyncExecutor;
        this.volcMemoryClient = volcMemoryClient;
        this.volcMemoryProperties = volcMemoryProperties;
        this.toolAgentGateway = toolAgentGateway;
        this.chatMessageGateway = chatMessageGateway;
        this.messageWindowChatMemory = MessageWindowChatMemory.builder()
            .build();
        this.ossCmdExe = ossCmdExe;
    }

    @Operation(summary = "登录")
    @PostMapping("/login")
    public Response login(@Valid @RequestBody LoginCmd loginCmd) {
        log.info("用户登录请求: {}", loginCmd.getUsername());

        try {
            // 先查询用户是否存在
            UserInfoEntity existingUser = userInfoGateway.findByUserId(loginCmd.getUsername());

            if (existingUser == null) {
                // 用户不存在，创建新用户
                UserInfoEntity newUser = new UserInfoEntity();
                newUser.setUserId(loginCmd.getUsername());
                newUser.setUsername(loginCmd.getUsername());
                newUser.setCreatedAt(LocalDateTime.now());
                newUser.setLastLoginAt(LocalDateTime.now());
                newUser.setBanned(0); // 0表示未封禁

                // 保存用户信息
                Long userId = userInfoGateway.insert(newUser);
                log.info("创建新用户成功，用户ID: {}, 数据库ID: {}", loginCmd.getUsername(), userId);

            } else {
                // 用户已存在，更新最后登录时间
                existingUser.setLastLoginAt(LocalDateTime.now());
                userInfoGateway.update(existingUser);
                log.info("用户登录成功，用户ID: {}", loginCmd.getUsername());

            }
            return Response.buildSuccess();

        } catch (Exception e) {
            log.error("登录失败，用户ID: {}", loginCmd.getUsername(), e);
            return Response.buildFailure("500", "登录失败，系统错误");
        }
    }

    @Operation(summary = "获取Agent模板列表")
    @PostMapping("/agent-templates")
    public MultiResponse<AgentTemplateCo> getAgentTemplates() {
        try {
            // 查询所有Agent模板
            List<AgentTemplateEntity> agentTemplates = agentTemplateGateway.findAll();
            log.info("获取Agent模板列表成功，数量: {}", agentTemplates.size());
            return MultiResponse.of(BeanCopyUtil.copyList(agentTemplates, AgentTemplateCo.class));
        } catch (Exception e) {
            log.error("获取Agent模板列表失败", e);
            return MultiResponse.buildFailure("500", "获取Agent模板列表失败，系统错误", AgentTemplateCo.class);
        }
    }

    @Operation(summary = "模型列表")
    @PostMapping("/model/list")
    public MultiResponse<ModelCo> modelList() {
        return MultiResponse.of(
            Arrays.stream(Model.values())
                .map(i -> ModelCo.builder().name(i.getName()).value(i.getValue()).build())
                .collect(Collectors.toList())
        );
    }

    @Operation(summary = "获取Agent详情信息")
    @PostMapping("/agent-template/info/{agentId}")
    public SingleResponse<AgentTemplateCo> getAgentInfo(@PathVariable("agentId") String agentId) {
        try {
            // 查询所有Agent模板
            AgentTemplateEntity agentTemplates = agentTemplateGateway.findByAgentId(agentId);
            return SingleResponse.of(BeanCopyUtil.copyOf(agentTemplates, AgentTemplateCo.class));
        } catch (Exception e) {
            log.error("获取Agent详情失败", e);
            return SingleResponse.buildFailure("500", "获取Agent详情失败，系统错误", AgentTemplateCo.class);
        }
    }

    @Operation(summary = "拷贝Agent智能体")
    @PostMapping("/agent-template/copy")
    public Response agentTemplateCopy(@RequestBody AgentTemplateCopy copyCo) {
        try {
            AgentTemplateEntity agentTemplates = agentTemplateGateway.findByAgentId(copyCo.getAgentId());
            AgentTemplateEntity newAgent = BeanCopyUtil.copyOf(agentTemplates, AgentTemplateEntity::new);
            newAgent.setId(null);
            newAgent.setAgentId("A" + RandomUtil.randomNumbers(8));
            newAgent.setName(copyCo.getName());
            newAgent.setCreateTime(null);
            newAgent.setCreatePerson(null);
            newAgent.setUpdatePerson(null);
            newAgent.setUpdateTime(null);
            if (!copyCo.copyNaturalAttributes) {
                newAgent.setNaturalAttributes(null);
            }
            if (!copyCo.copyTemperamentProfile) {
                newAgent.setTemperamentProfile(null);
            }
            if (!copyCo.copyInteractionProfile) {
                newAgent.setInteractionProfile(null);
            }
            if (!copyCo.copyTraitProfile) {
                newAgent.setTraitProfile(null);
            }
            if (!copyCo.copyChatModel) {
                newAgent.setChatModel(null);
            }
            if (!copyCo.copySystemPrompt) {
                newAgent.setSystemPrompt(null);
            }
            if (!copyCo.copySystemPrompt) {
                newAgent.setUserPrompt(null);
            }
            newAgent.save();
            return SingleResponse.of(BeanCopyUtil.copyOf(agentTemplates, AgentTemplateCo.class));
        } catch (Exception e) {
            log.error("Agent拷贝失败", e);
            return Response.buildFailure("500", "获取Agent详情失败，系统错误");
        }
    }

    @Operation(summary = "删除Agent智能体模板")
    @PostMapping("/agent-template/delete/{agentId}")
    public Response agentTemplateDelete(@PathVariable("agentId") String agentId) {
        AgentTemplateEntity agentTemplates = agentTemplateGateway.findByAgentId(agentId);
        agentTemplates.delete();
        return Response.buildSuccess();
    }

    @Operation(summary = "Agent模板更新")
    @PostMapping("/agent-template/update")
    public Response agentTemplateUpdate(@RequestBody AgentTemplateCo userAgentCo) {
        AgentTemplateEntity templateAgent = agentTemplateGateway.findByAgentId(userAgentCo.getAgentId());
        if (StrUtil.isNotBlank(userAgentCo.getName())) {
            templateAgent.setName(userAgentCo.getName());
        }
        if (StrUtil.isNotBlank(userAgentCo.getNaturalAttributes())) {
            templateAgent.setNaturalAttributes(userAgentCo.getNaturalAttributes());
        }
        if (StrUtil.isNotBlank(userAgentCo.getTemperamentProfile())) {
            templateAgent.setTemperamentProfile(userAgentCo.getTemperamentProfile());
        }
        if (StrUtil.isNotBlank(userAgentCo.getInteractionProfile())) {
            templateAgent.setInteractionProfile(userAgentCo.getInteractionProfile());
        }
        if (StrUtil.isNotBlank(userAgentCo.getTraitProfile())) {
            templateAgent.setTraitProfile(userAgentCo.getTraitProfile());
        }
        if (StrUtil.isNotBlank(userAgentCo.getSystemPrompt())) {
            templateAgent.setSystemPrompt(userAgentCo.getSystemPrompt());
        }
        if (StrUtil.isNotBlank(userAgentCo.getUserPrompt())) {
            templateAgent.setUserPrompt(userAgentCo.getUserPrompt());
        }
        templateAgent.update();
        return Response.buildSuccess();
    }

    @Operation(summary = "获取工具Agent详情信息")
    @PostMapping("/tool-agent/info/{agentId}")
    public SingleResponse<ToolAgentCo> getToolAgentInfo(@PathVariable("agentId") String agentId) {
        try {
            // 查询工具Agent
            ToolAgentEntity entity = toolAgentGateway.findByAgentId(agentId);
            if (entity == null) {
                log.info("工具Agent不存在: {}", agentId);
                return SingleResponse.buildFailure("404", "工具Agent不存在", ToolAgentCo.class);
            }
            return SingleResponse.of(BeanCopyUtil.copyOf(entity, ToolAgentCo.class));
        } catch (Exception e) {
            log.error("获取工具Agent详情失败", e);
            return SingleResponse.buildFailure("500", "获取工具Agent详情失败，系统错误", ToolAgentCo.class);
        }
    }

    @Operation(summary = "更新工具Agent信息")
    @PostMapping("/tool-agent/update")
    public Response toolAgentUpdate(@RequestBody ToolAgentCo toolAgentCo) {
        ToolAgentEntity entity = toolAgentGateway.findByAgentId(toolAgentCo.getAgentId());
        if (entity == null) {
            log.info("工具Agent不存在: {}", toolAgentCo.getAgentId());
            return Response.buildFailure("404", "工具Agent不存在，无法更新");
        }

        // 更新现有记录
        if (StrUtil.isNotBlank(toolAgentCo.getSystemPrompt())) {
            entity.setSystemPrompt(toolAgentCo.getSystemPrompt());
        }
        if (StrUtil.isNotBlank(toolAgentCo.getUserPrompt())) {
            entity.setUserPrompt(toolAgentCo.getUserPrompt());
        }
        if (StrUtil.isNotBlank(toolAgentCo.getChatModel())) {
            entity.setChatModel(toolAgentCo.getChatModel());
        }
        if (StrUtil.isNotBlank(toolAgentCo.getOutputVariable())) {
            entity.setOutputVariable(toolAgentCo.getOutputVariable());
        }
        if (toolAgentCo.getOutputVariableType() != null) {
            entity.setOutputVariableType(toolAgentCo.getOutputVariableType());
        }
        entity.update();
        return Response.buildSuccess();
    }

    @Operation(summary = "历史消息记录查询")
    @PostMapping("/chat-message/fetch")
    public MultiResponse<ChatMessageCo> chatMessageFetch(@Valid @RequestBody ChatMessageRequest request, HttpServletRequest httpRequest) {
        try {
            // 从cookie中获取用户信息
            String userId = getUserIdFromCookie(httpRequest);
            if (userId == null) {
                return MultiResponse.buildFailure("401", "用户未登录", ChatMessageCo.class);
            }

            // 验证用户是否存在
            UserInfoEntity userInfo = userInfoGateway.findByUserId(userId);
            if (userInfo == null) {
                return MultiResponse.buildFailure("404", "用户不存在", ChatMessageCo.class);
            }

            // 查询历史消息
            List<ChatMessageEntity> historyMessages = chatMessageGateway.findHistoryMessages(
                userId,
                request.getAgentId(),
                request.getBeforeId(),
                request.getLimit()
            );

            // 转换为CO对象
            List<ChatMessageCo> messageCoList = BeanCopyUtil.copyList(historyMessages, ChatMessageCo.class);

            log.info("查询历史消息成功，用户ID: {}, 智能体ID: {}, 消息数量: {}",
                userId, request.getAgentId(), messageCoList.size());

            return MultiResponse.of(messageCoList);

        } catch (Exception e) {
            log.error("查询历史消息失败", e);
            return MultiResponse.buildFailure("500", "查询历史消息失败，系统错误", ChatMessageCo.class);
        }
    }

    @Operation(summary = "消息记录清空")
    @PostMapping("/chat-message/clear/{agentId}")
    public Response chatMessageClear(@PathVariable("agentId") String agentId, HttpServletRequest httpRequest) {
        try {
            // 从cookie中获取用户信息
            String userId = getUserIdFromCookie(httpRequest);
            if (userId == null) {
                return MultiResponse.buildFailure("401", "用户未登录", ChatMessageCo.class);
            }

            // 清空历史消息
            chatMessageGateway.clearHistoryMessages(userId, agentId);
            return Response.buildSuccess();

        } catch (Exception e) {
            log.error("查询历史消息失败", e);
            return Response.buildFailure("500", "清除历史消息失败，系统错误");
        }
    }

    @Operation(summary = "清除上下文")
    @PostMapping("/chat-message/chat-memory/clear/{agentId}")
    public Response chatMemoryClear(@PathVariable("agentId") String agentId, HttpServletRequest httpRequest) {
        try {
            String userId = getUserIdFromCookie(httpRequest);
            String conversationId = userId + ":" + agentId;
            String key = DEFAULT_KEY_PREFIX + conversationId;
            RedisUtil.del(key);
            return Response.buildSuccess();
        } catch (Exception e) {
            log.error("清除上下文失败", e);
            return Response.buildFailure("500", "清除上下文失败，系统错误");
        }
    }

    @Operation(summary = "长期记忆-用户画像")
    @PostMapping("/agent-memory/user-profile/{agentId}")
    public SingleResponse<String> agentMemoryUserProfile(@PathVariable("agentId") String agentId, HttpServletRequest httpRequest) {
        return SingleResponse.of(fetchVolcMemory(agentId, getUserIdFromCookie(httpRequest)));
    }

    @Operation(summary = "oss sts授权", description = "oss sts授权")
    @GetMapping("/sts")
    public SingleResponse<OssStsCo> sts() {
        return ossCmdExe.sts();
    }

    @Operation(summary = "流式聊天")
    @PostMapping(value = "/chat/stream")
    public SseEmitter streamChat(@Valid @RequestBody ChatStreamRequest request,
                                 HttpServletRequest httpRequest) {
        log.info("流式聊天请求: agentId={}, message={}", request.getAgentId(), request.getMessage());
        SseEmitter emitter = new SseEmitter(60000L); // 60秒超时

        // 设置超时和错误处理
        emitter.onTimeout(emitter::complete);
        emitter.onError((throwable) -> emitter.complete());

        CompletableFuture.runAsync(() -> sseChat(request, httpRequest, emitter), asyncExecutor);
        return emitter;
    }

    private void sseChat(ChatStreamRequest request, HttpServletRequest httpRequest, SseEmitter emitter) {
        try {
            // 从cookie中获取用户信息
            String userId = getUserIdFromCookie(httpRequest);
            if (userId == null) {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data("{\"error\":\"用户未登录\"}"));
                emitter.complete();
                return;
            }

            // 验证用户是否存在
            UserInfoEntity userInfo = userInfoGateway.findByUserId(userId);
            if (userInfo == null) {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data("{\"error\":\"用户不存在\"}"));
                emitter.complete();
                return;
            }

            // 获取Agent模板信息
            AgentTemplateEntity agentTemplate = agentTemplateGateway.findByAgentId(request.getAgentId());
            if (agentTemplate == null) {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data("{\"error\":\"智能体不存在\"}"));
                emitter.complete();
                return;
            }

            UserAgentEntity userAgentEntity = userAgentGateway.getOrCreate(userId, agentTemplate);

            Map<String, Object> contextParam = Maps.newHashMap();
            contextParam.put("user_id", userId);
            contextParam.put("agent_name", agentTemplate.getName());
            contextParam.put("natural_attributes", agentTemplate.getNaturalAttributes());
            contextParam.put("temperament_profile", agentTemplate.getTemperamentProfile());
            contextParam.put("trait_profile", userAgentEntity.getTraitProfile());
            contextParam.put("interaction_profile", userAgentEntity.getInteractionProfile());
            contextParam.put("user_input", request.getMessage());

            try {
                // 会话ID、聊天记忆、上一轮情绪
                String conversationId = userId + ":" + request.getAgentId();
                String chatMemory = getLatestMemoryString(conversationId, 5);
                contextParam.put("chat_memory", chatMemory);
                contextParam.put("conversation_id", conversationId);
                Object latestEmotion = RedisUtil.get(conversationId + ":emotion:latest");
                contextParam.put("agent_last_emotion", latestEmotion == null ? "" : latestEmotion.toString());

                // 聊天上下文
                ChatContext chatContext = new ChatContext();
                chatContext.setQuestionId(snowflake.nextIdStr());
                chatContext.setAnswerId(snowflake.nextIdStr());
                chatContext.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
                chatContext.setUserId(userId);
                chatContext.setAgentId(request.getAgentId());
                chatContext.setConversationId(conversationId);
                chatContext.setQuestion(request.getMessage());

                // 0️⃣记忆库召回
                String userProfileMemory = fetchVolcMemory(userAgentEntity.getAgentId(), userId);
                log.info("Memory recall: {}", userProfileMemory);
                if (userProfileMemory != null) {
                    String progressCont = JSON.toJSONString(new SseData()
                        .setMsgId(chatContext.getAnswerId())
                        .setQuestionMsgId(chatContext.getQuestionId())
                        .setTitle("用户画像记忆召回")
                        .setContent(userProfileMemory));
                    chatContext.addProgress(progressCont);

                    emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(progressCont));
                }
                contextParam.put("user_profile", userProfileMemory);

                // ①情绪识别
                ToolAgentEntity recognizeEmotion = toolAgentGateway.findByAgentId("recognizeEmotion");
                ImmutablePair<DebuggerInfo, String> userEmotionPair = callToolAgent(recognizeEmotion, contextParam);
                if (userEmotionPair != null && userEmotionPair.getRight() != null) {
                    String progressCont = JSON.toJSONString(new SseData()
                        .setMsgId(chatContext.getAnswerId())
                        .setQuestionMsgId(chatContext.getQuestionId())
                        .setTitle("用户情绪识别")
                        .setDebugger(userEmotionPair.left)
                        .setContent(userEmotionPair.right));
                    chatContext.addProgress(progressCont);

                    emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(progressCont));
                }

                // ②情绪模拟
                ToolAgentEntity generateAgentEmotion = toolAgentGateway.findByAgentId("generateAgentEmotion");
                ImmutablePair<DebuggerInfo, String> agentEmotionPair = callToolAgent(generateAgentEmotion, contextParam);
                if (agentEmotionPair != null && agentEmotionPair.getRight() != null) {
                    String progressCont = JSON.toJSONString(new SseData()
                        .setMsgId(chatContext.getAnswerId())
                        .setQuestionMsgId(chatContext.getQuestionId())
                        .setTitle("Agent情绪模拟")
                        .setDebugger(agentEmotionPair.left)
                        .setContent(agentEmotionPair.right));
                    chatContext.addProgress(progressCont);

                    // 保存上一轮情绪
                    RedisUtil.set(conversationId + ":emotion:latest", agentEmotionPair.right);
                    // 给前端发送情绪
                    emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(progressCont));
                }

                // 创建消息列表
                List<Message> messages = new ArrayList<>();
                if (StrUtil.isNotBlank(agentTemplate.getSystemPrompt())) {
                    String format = StrUtil.format(agentTemplate.getSystemPrompt(), contextParam);
                    messages.add(new SystemMessage(format));
                }
                if (CollUtil.isNotEmpty(request.getImages())) {
                    List<Media> medias = request.getImages().stream().map(image -> {
                        try {
                            return Media.builder()
                                .data(new URI(image.getUrl()))
                                .name(image.getName())
                                .mimeType(MimeTypeUtils.parseMimeType(determineMimeTypeFromUrl(image.getUrl())))
                                .build();
                        } catch (URISyntaxException e) {
                            log.error(e.getMessage());
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    UserMessage message = UserMessage.builder()
                        .text(request.getMessage())
                        .media(medias)
                        .build();
                    messages.add(message);
                } else {
                    messages.add(new UserMessage(request.getMessage()));
                }
                //log.info("模型调用messages:\n{}", JSON.toJSONString(messages));

                // 创建ChatClient
                Model modelEnum = Model.getByValue(agentTemplate.getChatModel());
                Assert.notNull(modelEnum, "模型不存在");
                ChatClient chatClient = ChatClient.builder(SpringUtil.getBean(modelEnum.getChatModel(), ChatModel.class))
                    .defaultAdvisors(
                        // 这里使用Redis，因为Mysql有精度问题，待官方修复：https://github.com/spring-projects/spring-ai/pull/3154
                        MessageChatMemoryAdvisor
                            .builder(messageWindowChatMemory)
                            .conversationId(conversationId)
                            .build(),
                        // 对话增强
                        ChatRetrievalAugmentationAdvisor
                            .builder()
                            .chatContext(chatContext)
                            .contextParam(contextParam)
                            .build()
                    )
                    .build();


                // 发送开始事件
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(JSON.toJSONString(new SseData().setMsgId(chatContext.getAnswerId()).setQuestionMsgId(chatContext.getQuestionId()).setContent("started"))));

                // 流式调用
                Prompt chatPrompt = new Prompt(messages, ChatOptions.builder().model(modelEnum.getValue()).build());
                Flux<String> chatResponse = chatClient.prompt(chatPrompt)
                    .stream()
                    .content();

                // 处理流式响应
                chatResponse.subscribe(
                    content -> {
                        try {
                            // 拼接回答
                            chatContext.getAnswer().append(content);
                            // 发送内容块
                            emitter.send(SseEmitter.event()
                                .name("content")
                                .data(JSON.toJSONString(new SseData().setMsgId(chatContext.getAnswerId()).setQuestionMsgId(chatContext.getQuestionId()).setContent(content))));

                        } catch (IOException e) {
                            log.error("发送SSE消息失败", e);
                            emitter.completeWithError(e);
                        }
                    },
                    error -> {
                        log.error("聊天流式处理失败", error);
                        try {
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data(JSON.toJSONString(new SseData().setMsgId(chatContext.getAnswerId()).setQuestionMsgId(chatContext.getQuestionId()).setContent("聊天处理失败: " + error.getMessage()))));
                        } catch (IOException e) {
                            log.error("发送错误消息失败", e);
                        }
                        emitter.completeWithError(error);
                        // 保存聊天记录
                        saveChatMessage(chatContext);
                        // 更新记忆
                        syncMemory(chatContext);
                    },
                    () -> {
                        try {
                            // 发送结束事件
                            emitter.send(SseEmitter.event()
                                .name("end")
                                .data(JSON.toJSONString(new SseData().setMsgId(chatContext.getAnswerId()).setQuestionMsgId(chatContext.getQuestionId()).setContent("completed"))));
                        } catch (IOException e) {
                            log.error("发送结束消息失败", e);
                        }
                        emitter.complete();
                        // 保存聊天记录
                        saveChatMessage(chatContext);
                        // 更新记忆
                        syncMemory(chatContext);
                    }
                );

            } catch (Exception e) {
                log.error("聊天流式处理异常", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(JSON.toJSONString(new SseData().setContent("聊天处理异常: " + e.getMessage()))));
                } catch (IOException ex) {
                    log.error("发送异常消息失败", ex);
                }
                emitter.completeWithError(e);
            }

        } catch (Exception e) {
            log.error("流式聊天初始化失败", e);
            try {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data(JSON.toJSONString(new SseData().setContent("聊天初始化失败: " + e.getMessage()))));
            } catch (IOException ex) {
                log.error("发送初始化错误消息失败", ex);
            }
            emitter.completeWithError(e);
        }
    }

    private void syncMemory(ChatContext chatContext) {
        try {
            MemoryAddRequest request = new MemoryAddRequest();
            request.setCollectionName(volcMemoryProperties.getCollectionName());
            request.setSessionId(chatContext.getConversationId() + System.currentTimeMillis());
            request.setMessages(Arrays.asList(
                new MemoryAddRequest.Message()
                    .setRole(MessageType.USER.getValue())
                    .setContent(chatContext.getQuestion())
                    .setRoleId(chatContext.getUserId())
                    .setTime(System.currentTimeMillis()),
                new MemoryAddRequest.Message()
                    .setRole(MessageType.ASSISTANT.getValue())
                    .setContent(chatContext.getAnswer().toString())
                    .setRoleId(chatContext.getAgentId())
                    .setTime(System.currentTimeMillis())
            ));
            request.setMetadata(
                new MemoryAddRequest.Metadata()
                    .setDefaultUserId(chatContext.getUserId())
                    .setDefaultAssistantId(chatContext.getAgentId())
                    .setTime(System.currentTimeMillis())
            );
            MemoryAddResponse response = volcMemoryClient.addMemory(request);
            log.info("addMemory response: \n{}", response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void saveChatMessage(ChatContext chat) {
        String chatId = snowflake.nextIdStr();
        String requestId = MDC.get(RequestConstant.REQUEST_ID);

        ChatMessageEntity questionEntity = new ChatMessageEntity();
        questionEntity.setUserId(chat.getUserId());
        questionEntity.setAgentId(chat.getAgentId());
        questionEntity.setConversationId(chat.getConversationId());
        questionEntity.setMsgId(snowflake.nextIdStr());
        questionEntity.setChatId(chatId);
        questionEntity.setRole(MessageType.USER.getValue());
        questionEntity.setQuestion(chat.getQuestion());
        questionEntity.setContent(chat.getQuestion());

        ChatMessageEntity answerEntity = new ChatMessageEntity();
        answerEntity.setUserId(chat.getUserId());
        answerEntity.setAgentId(chat.getAgentId());
        answerEntity.setConversationId(chat.getConversationId());
        answerEntity.setMsgId(snowflake.nextIdStr());
        answerEntity.setChatId(chatId);
        answerEntity.setRole(MessageType.ASSISTANT.getValue());
        answerEntity.setQuestion(chat.getQuestion());
        answerEntity.setContent(chat.getAnswer().toString());
        answerEntity.setExt(new ChatMsgExt().setProgressList(chat.getProgressList()).setRequestId(requestId));

        chatMessageGateway.insertBatch(Arrays.asList(questionEntity, answerEntity));
    }

    private ImmutablePair<DebuggerInfo, String> callToolAgent(ToolAgentEntity toolAgentEntity, Map<String, Object> contextParam) {
        // 调用模型进行情绪识别
        try {
            long startTime = System.currentTimeMillis();
            // 创建ChatClient
            Model modelEnum = Model.getByValue(toolAgentEntity.getChatModel());
            Assert.notNull(modelEnum, "模型不存在");
            ChatClient chatClient = ChatClient.builder(SpringUtil.getBean(modelEnum.getChatModel(), ChatModel.class)).build();

            // 用户提示词
            String systemPrompt = StrUtil.format(toolAgentEntity.getSystemPrompt(), contextParam);
            log.info("{} prompt: {}", toolAgentEntity.getName(), systemPrompt);

            String userPrompt = StrUtil.format(toolAgentEntity.getUserPrompt(), contextParam);

            String emotionResult = chatClient.prompt()
                .options(ChatOptions.builder().model(modelEnum.getValue()).build())
                .system(systemPrompt)
                .user(userPrompt)
                .call()
                .content();

            log.info("{} 结果: {}", toolAgentEntity.getName(), emotionResult);

            // 添加至上下文
            contextParam.put(toolAgentEntity.getOutputVariable(), emotionResult);

            DebuggerInfo debuggerInfo = new DebuggerInfo()
                .setModel(modelEnum.getValue())
                //.setInput()
                .setOutput(emotionResult)
                .setCost(System.currentTimeMillis() - startTime);
            return ImmutablePair.of(debuggerInfo, emotionResult);

        } catch (Exception e) {
            log.error("工具Agent调用失败", e);
            return ImmutablePair.of(null, null);
        }

    }

    private String getLatestMemoryString(String conversationId, int round) {
        // 构建最近5轮对话内容
        StringBuilder conversationHistory = new StringBuilder();

        // 保留最近5轮问答（正序，从后往前取最多10条消息，即5轮问答）
        List<Message> recentMessages = new ArrayList<>();
        List<Message> memoryMessages = this.messageWindowChatMemory.get(conversationId);
        if (!memoryMessages.isEmpty()) {
            // 取最后10条消息（最多5轮问答）
            int startIndex = Math.max(0, memoryMessages.size() - round * 2);
            recentMessages = memoryMessages.subList(startIndex, memoryMessages.size());
        }

        // 构建对话历史
        for (Message message : recentMessages) {
            if (message instanceof UserMessage) {
                conversationHistory.append("用户：").append(message.getText()).append("\n");
            } else if (message instanceof AssistantMessage) {
                conversationHistory.append("AI：").append(message.getText()).append("\n");
            }
        }
        return conversationHistory.toString();
    }

    private String fetchVolcMemory(String agentId, String userId) {
        try {
            MemorySearchRequest memorySearchRequest = new MemorySearchRequest();
            memorySearchRequest.setCollectionName(volcMemoryProperties.getCollectionName());
            memorySearchRequest.setFilter(new MemorySearchRequest.Filter()
                .setUserId(List.of(userId))
                // FIXME 待确认
                // .setAssistantId(List.of(agentId))
                .setMemoryType(List.of("sys_profile_v1", "sys_profile_collect_v1"))
            );
            MemorySearchResponse memorySearchResponse = volcMemoryClient.searchMemory(memorySearchRequest);
            log.info("memorySearchResponse: \n{}", JSON.toJSONString(memorySearchResponse));
            if (memorySearchResponse.getCode() != 0) {
                throw new BizException(String.valueOf(memorySearchResponse.getCode()), memorySearchResponse.getMessage());
            }

            // 取出memory_info
            return Optional.of(memorySearchResponse)
                .map(MemorySearchResponse::getData)
                .map(MemorySearchResponse.DataInfo::getResultList)
                .filter(list -> !list.isEmpty())
                .flatMap(list -> list.stream()
                    .filter(item -> "sys_profile_v1".equals(item.getMemoryType()))
                    .findFirst())
                .map(MemorySearchResponse.ResultItem::getMemoryInfo)
                .map(memoryInfo -> memoryInfo.getString("user_profile"))
                .orElse(StrUtil.EMPTY);

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    private String getUserIdFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("userId".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据URL确定MIME类型
     * @param imageUrl 图片URL
     * @return MIME类型字符串
     */
    private String determineMimeTypeFromUrl(String imageUrl) {
        String lowerUrl = imageUrl.toLowerCase();
        if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerUrl.endsWith(".png")) {
            return "image/png";
        } else if (lowerUrl.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerUrl.endsWith(".webp")) {
            return "image/webp";
        } else {
            // 默认使用JPEG
            return "image/jpeg";
        }
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "用户智能体")
    public static class AgentTemplateCo {

        @Schema(description = "智能体唯一ID")
        private String agentId;

        @Schema(description = "名称")
        private String name;

        @Schema(description = "自然属性设定（只读）")
        private String naturalAttributes;

        @Schema(description = "气质人格设定（只读）")
        private String temperamentProfile;

        @Schema(description = "交互设定（可变）")
        private String interactionProfile;

        @Schema(description = "特质人格设定（可变）")
        private String traitProfile;

        @Schema(description = "系统提示词")
        private String systemPrompt;

        @Schema(description = "用户提示词")
        private String userPrompt;

        @Schema(description = "模型")
        private String chatModel;

    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "工具智能体")
    public static class ToolAgentCo {

        @Schema(description = "智能体唯一ID")
        private String agentId;

        @Schema(description = "系统提示词")
        private String systemPrompt;

        @Schema(description = "用户提示词")
        private String userPrompt;

        @Schema(description = "聊天模型")
        private String chatModel;

        @Schema(description = "输出变量")
        private String outputVariable;

        @Schema(description = "输出变量类型")
        private Integer outputVariableType;

    }

    @Data
    @Schema(description = "登录命令对象")
    public static class LoginCmd {

        @Schema(description = "用户名", required = true)
        @NotBlank(message = "用户名不能为空")
        @Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
        @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字、下划线")
        private String username;

    }

    @Data
    @Schema(description = "流式聊天请求对象")
    public static class ChatStreamRequest {

        @Schema(description = "智能体ID", required = true)
        @NotBlank(message = "智能体ID不能为空")
        private String agentId;

        @Schema(description = "消息内容", required = true)
        @NotBlank(message = "消息内容不能为空")
        @Size(max = 2000, message = "消息长度不能超过2000个字符")
        private String message;

        @Schema(description = "图片列表")
        private List<ImageInfo> images;

    }

    @Data
    @Schema(description = "图像Info对象")
    public static class ImageInfo {

        @Schema(description = "图片地址", required = true)
        @NotBlank(message = "图片地址不能为空")
        private String url;

        @Schema(description = "图片名称，示例：123.png", required = true)
        @NotBlank(message = "图片名称不能为空")
        private String name;

    }

    @Data
    @Builder
    @Schema(description = "模型响应对象")
    public static class ModelCo {

        @Schema(description = "模型名称")
        private String name;

        @Schema(description = "模型值")
        private String value;

    }

    @Getter
    @AllArgsConstructor
    public enum Model {
        QWEN_MAX("Qwen-Max", "qwen-max", "openAiChatModel"),
        QWEN_PLUS("Qwen-Plus", "qwen-plus", "openAiChatModel"),
        GPT_4O("GPT-4o", "gpt-4o", "openAiChatModel"),
        Claude_Sonnet_4("Claude Sonnet 4", "claude-sonnet-4-20250514", "openAiChatModel"),
        Moonshot_v1("Moonshot-v1", "moonshot-v1-32k", "openAiChatModel"),
        MiniMax_Text_01("MiniMax-Text-01", "MiniMax-Text-01", "openAiChatModel"),
        Gemini2_5_PRO("Gemini-2.5-pro", "gemini-2.5-pro", "openAiChatModel"),
        Gemini2_5_FLASH("Gemini-2.5-flash", "gemini-2.5-flash", "openAiChatModel"),
        DEEPSEEK_V3("DeepSeek-V3", "deepseek-v3-0324", "openAiChatModel"),
        DEEPSEEK_R1("DeepSeek-R1", "deepseek-r1-0528", "deepSeekChatModel");

        private final String name;
        private final String value;
        private final String chatModel;

        public static Model getByValue(String value) {
            for (Model model : values()) {
                if (model.value.equals(value)) {
                    return model;
                }
            }
            return null;
        }
    }

    @Data
    @Schema(description = "聊天记录请求对象")
    public static class ChatMessageRequest {

        @Schema(description = "智能体ID", required = true)
        @NotBlank(message = "智能体ID不能为空")
        private String agentId;

        @Schema(description = "查看指定位置之前的消息。默认为 0，表示不指定位置。如需向前翻页，则指定为返回结果中的msgId")
        private String beforeId = "0";

        @Schema(description = "每页数量")
        private Integer limit = 10;

    }

    @Data
    public static class ChatMessageCo {
        private String userId;
        private String agentId;
        private String conversationId;
        private String msgId;
        private String role;
        private String chatId;
        private String content;
        private ChatMsgExt ext;
        private LocalDateTime createTime;
    }

    @Data
    @Accessors(chain = true)
    public static class ChatContext {
        private String requestId;
        private String questionId;
        private String answerId;
        private String userId;
        private String agentId;
        private String conversationId;
        private String question;
        private StringBuilder answer = new StringBuilder();
        private List<String> progressList = new ArrayList<>();

        public void addProgress(String progress) {
            progressList.add(progress);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class SseData {
        private String msgId;
        private String questionMsgId;

        private String title;
        private String content;
        private DebuggerInfo debugger;
    }

    @Data
    @Accessors(chain = true)
    public static class DebuggerInfo {
        private String model;
        private Long cost;
        private String input;
        private String output;
    }

    @Data
    @Accessors(chain = true)
    public static class AgentTemplateCopy {
        @NotBlank(message = "智能体ID不能为空")
        private String agentId;
        @NotBlank(message = "智能体名称不能为空")
        private String name;
        @Schema(description = "是否拷贝 自然属性")
        private boolean copyNaturalAttributes = true;
        @Schema(description = "是否拷贝 气质人格")
        private boolean copyTemperamentProfile = true;
        @Schema(description = "是否拷贝 交互设定")
        private boolean copyInteractionProfile = true;
        @Schema(description = "是否拷贝 特质人格")
        private boolean copyTraitProfile = true;
        @Schema(description = "是否拷贝 模型配置")
        private boolean copyChatModel = true;
        @Schema(description = "是否拷贝 系统提示词")
        private boolean copySystemPrompt = true;
        @Schema(description = "是否拷贝 用户提示词")
        private boolean copyUserPrompt = true;
    }

}

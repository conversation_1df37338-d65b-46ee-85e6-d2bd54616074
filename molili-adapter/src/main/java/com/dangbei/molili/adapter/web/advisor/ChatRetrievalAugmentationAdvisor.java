package com.dangbei.molili.adapter.web.advisor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.dangbei.molili.adapter.web.PlaygroundController;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.AdvisorChain;

import java.util.HashMap;
import java.util.Map;

/**
 * 聊天检索增强
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@Slf4j
public class ChatRetrievalAugmentationAdvisor extends BusinessBaseAdvisor {

    private final int order;
    private final PlaygroundController.ChatContext chatContext;
    private final Map<String, Object> contextParam;

    private static final String REWRITE_USER_PROMPT = """
        请根据以下设定的情绪风格来回答用户的问题，确保回答在语气、用词和表达方式上都与该情绪保持一致：
        {agent_emotion}

        用户提问如下：
        {user_input}
        """;

    public ChatRetrievalAugmentationAdvisor(int order, PlaygroundController.ChatContext chatContext, Map<String, Object> contextParam) {
        this.order = order;
        this.chatContext = chatContext;
        this.contextParam = contextParam;
    }

    @NotNull
    @Override
    public ChatClientRequest before(@NotNull ChatClientRequest chatClientRequest, @NotNull AdvisorChain advisorChain) {
        Map<String, Object> context = new HashMap<>(chatClientRequest.context());

        // TODO 找到System信息，增加记忆点


        // 找到User信息，增加情绪向量
        String agentEmotion = Convert.toStr(contextParam.get("agent_emotion"));
        if (StrUtil.isNotBlank(agentEmotion)) {
            return chatClientRequest.mutate()
                .prompt(chatClientRequest.prompt().augmentUserMessage(StrUtil.format(REWRITE_USER_PROMPT, contextParam)))
                .context(context)
                .build();
        }
        return chatClientRequest;
    }

    @NotNull
    @Override
    public ChatClientResponse after(@NotNull ChatClientResponse chatClientResponse, @NotNull AdvisorChain advisorChain) {
        return chatClientResponse;
    }

    @Override
    public int getOrder() {
        return this.order;
    }

    public static ChatRetrievalAugmentationAdvisor.Builder builder() {
        return new ChatRetrievalAugmentationAdvisor.Builder();
    }

    public static final class Builder {
        private int order;
        private PlaygroundController.ChatContext chatContext;
        private Map<String, Object> contextParam;

        public ChatRetrievalAugmentationAdvisor.Builder chatContext(PlaygroundController.ChatContext chatContext) {
            this.chatContext = chatContext;
            return this;
        }

        public ChatRetrievalAugmentationAdvisor.Builder order(Integer order) {
            this.order = order;
            return this;
        }

        public ChatRetrievalAugmentationAdvisor.Builder contextParam(Map<String, Object> contextParam) {
            this.contextParam = contextParam;
            return this;
        }

        public ChatRetrievalAugmentationAdvisor build() {
            return new ChatRetrievalAugmentationAdvisor(this.order, this.chatContext, this.contextParam);
        }
    }

}

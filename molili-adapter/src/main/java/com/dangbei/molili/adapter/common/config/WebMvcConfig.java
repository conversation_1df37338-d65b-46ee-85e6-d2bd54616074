package com.dangbei.molili.adapter.common.config;

import com.alibaba.cola.common.constant.DateTimeConstant;
import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.common.util.HttpUtil;
import jakarta.servlet.DispatcherType;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.lang.Nullable;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

/**
 * 会话登录拦截器
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022-04-02
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 默认请求日志打印大小，业务方可以按需调整
     **/
    private static final int DEFAULT_MAX_REQUEST_LOG_LENGTH = 1024 * 10;

    /**
     * 过滤不需要打印的 path，一般例如上传文件、健康检查、swagger-ui 相关请求等，在这里维护
     **/
    private static final Set<String> IGNORE_LOG_REQUEST_PATH = Set.of(
        "/molili/health",
        "/molili/healthApi/v1/liveness",
        "/molili/swagger-ui/swagger-initializer.js",
        "/molili/swagger-ui/index.html",
        "/molili/swagger-ui/swagger-ui.css",
        "/molili/swagger-ui/swagger-ui-standalone-preset.js",
        "/molili/swagger-ui/swagger-ui-bundle.js",
        "/molili/v3/api-docs/swagger-config",
        "/molili/v3/api-docs");

    /**
     * 过滤不需要打印响应体的 path，比如排除媒资搜索结果这种可能产生大响应体的内容
     **/
    private static final Set<String> IGNORE_LOG_RESPONSE_PATH = Set.of(
        "/molili/playground/v1/chat/stream",
        "/molili/health",
        "/molili/**.html",
        "/molili/**.css",
        "/molili/**.js",
        "/molili/components/**.js"
    );


    /**
     * 使用过滤器处理跨域，保证优先级最高
     * @return 跨域过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.addAllowedMethod("*");
        config.addAllowedHeader("*");
        config.setAllowCredentials(true);
        config.setMaxAge(3600L);
        UrlBasedCorsConfigurationSource configSource = new UrlBasedCorsConfigurationSource();
        configSource.registerCorsConfiguration("/**", config);
        return new CorsFilter(configSource);
    }

    /**
     * 注册请求日志过滤器
     * @return {@link FilterRegistrationBean }<{@link HttpFilter }>
     */
    @Bean
    public FilterRegistrationBean<HttpFilter> contentCachingRequestFilterRegistration() {
        FilterRegistrationBean<HttpFilter> contentFilterRegistration = new FilterRegistrationBean<>();
        contentFilterRegistration.setFilter(contentCachingRequestFilter());
        contentFilterRegistration.setDispatcherTypes(DispatcherType.ASYNC, DispatcherType.REQUEST);
        return contentFilterRegistration;
    }

    /**
     * 请求日志过滤器的核心逻辑
     * @return {@link HttpFilter }
     */
    public HttpFilter contentCachingRequestFilter() {

        return new HttpFilter() {
            @Override
            protected void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
                // 优先取上游 header 携带的 requestId，取不到再取x-request-id，没有就自己生成一个
                if (StringUtils.isBlank(MDC.get(RequestConstant.REQUEST_ID))) {
                    String requestId = request.getHeader(RequestConstant.REQUEST_ID);
                    if (StringUtils.isBlank(requestId)) {
                        requestId = request.getHeader(RequestConstant.X_REQUEST_ID);
                    }
                    if (StringUtils.isBlank(requestId)) {
                        requestId = UUID.randomUUID().toString();
                    }
                    MDC.put(RequestConstant.REQUEST_ID, requestId);
                }
                try {
                    String path = request.getRequestURI();
                    // 判断是否要跳过 request 内容打印
                    boolean skippedRequestLog = skipRequestLog(path);
                    // 判断是否要跳过 response 内容打印
                    boolean skipResponseLog = skipResponseLog(path);

                    // 跳过不需要打印的 path
                    if (skippedRequestLog && skipResponseLog) {
                        chain.doFilter(request, response);
                    } else {
                        if (request.getDispatcherType() == DispatcherType.REQUEST) {
                            request.setAttribute("beginTimeStamp", System.currentTimeMillis());
                        }
                        HttpServletRequest servletRequest = request;
                        if (!skippedRequestLog) {
                            servletRequest = !(request instanceof CachedBodyRequestWrapper) ?
                                new CachedBodyRequestWrapper(request) : (CachedBodyRequestWrapper) request;
                        }
                        HttpServletResponse servletResponse = response;
                        if (!skipResponseLog) {
                            servletResponse = !(response instanceof ContentCachingResponseWrapper) ?
                                new ContentCachingResponseWrapper(response) : (ContentCachingResponseWrapper) response;
                        }
                        if (request.getDispatcherType() == DispatcherType.REQUEST) {
                            handleRequest(skippedRequestLog, servletRequest);
                        }
                        chain.doFilter(servletRequest, servletResponse);
                        if (request.getDispatcherType() == DispatcherType.REQUEST) {
                            if (!request.isAsyncStarted()) {
                                handleResponse(skipResponseLog, servletRequest, servletResponse);
                            }
                        } else if (request.getDispatcherType() == DispatcherType.ASYNC) {
                            handleResponse(skipResponseLog, servletRequest, servletResponse);
                        }
                    }
                } finally {
                    MDC.clear();
                }
            }
        };
    }

    /**
     * 是否跳过打印 request 日志
     * @param path 路径
     * @return boolean
     */
    private boolean skipRequestLog(String path) {
        return StringUtils.isNotBlank(path) && IGNORE_LOG_REQUEST_PATH.contains(path);
    }

    /**
     * 是否要跳过 response 内容打印
     * @param path 路径
     * @return boolean
     */
    private boolean skipResponseLog(String path) {
        if (StringUtils.isNotBlank(path)) {
            for (String pattern : IGNORE_LOG_RESPONSE_PATH) {
                if (pathMatcher.match(pattern, path)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 处理请求体，打印请求信息
     */
    private void handleRequest(boolean skippedRequestLog, HttpServletRequest request) {
        if (!skippedRequestLog && !HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
            StringBuilder msg = new StringBuilder();
            msg.append(request.getMethod()).append(' ');
            msg.append(request.getRequestURI());
            String queryString = request.getQueryString();
            if (StringUtils.isNotBlank(queryString)) {
                msg.append('?').append(queryString);
            }
            HttpHeaders headers = new ServletServerHttpRequest(request).getHeaders();
            msg.append(", headers=").append(headers);
            msg.append(", ip=").append(HttpUtil.getIp(request));
            log.info("[RequestStart]:{}，payload:{}", msg, getMessagePayload(request));
        }
    }

    /**
     * 处理响应体，打印响应内容，以及请求的 payload 信息
     * @param skipResponse 是否要跳过 response 内容打印
     * @param request      请求包装器
     * @param response     响应包装器
     * @throws IOException IOException
     */
    private void handleResponse(boolean skipResponse, HttpServletRequest request,
                                HttpServletResponse response) throws IOException {
        if (!skipResponse) {
            log.info("[RequestFinish]:{},url:{},payload:{},cost:{}ms", getResponseContent(response),
                request.getRequestURI(),
                getMessagePayload(request),
                System.currentTimeMillis() - (long) request.getAttribute("beginTimeStamp"));
        }
        if (Objects.nonNull(response) && response instanceof ContentCachingResponseWrapper responseWrapper) {
            responseWrapper.copyBodyToResponse();
        }
    }

    /**
     * 获取 payload 信息
     * @param request 请求包装器
     * @return {@link String }
     */
    @Nullable
    private String getMessagePayload(HttpServletRequest request) {
        if (Objects.nonNull(request) && request instanceof CachedBodyRequestWrapper requestWrapper) {
            return requestWrapper.getBody();
        }
        return null;
    }

    /**
     * 获取响应体信息
     * @param response 响应包装器
     * @return {@link String }
     */
    @Nullable
    private String getResponseContent(HttpServletResponse response) {
        if (Objects.nonNull(response) && response instanceof ContentCachingResponseWrapper responseWrapper) {
            byte[] buf = responseWrapper.getContentAsByteArray();
            return wrapperByteToStr(buf, StandardCharsets.UTF_8.name());
        }
        return null;
    }

    /**
     * 将 byte 转 String
     * @param buf               缓冲器
     * @param characterEncoding 字符编码
     * @return {@link String }
     */
    @Nullable
    private String wrapperByteToStr(byte[] buf, String characterEncoding) {
        if (buf.length > 0) {
            int length = Math.min(buf.length, DEFAULT_MAX_REQUEST_LOG_LENGTH);
            try {
                return new String(buf, 0, length, characterEncoding);
            } catch (UnsupportedEncodingException ex) {
                return "[unknown]";
            }
        }
        return null;
    }

    /**
     * 添加格式化器
     * @param registry 格式化器注册表
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        // Params LocalDate 类型入参格式化
        registry.addConverter(String.class, LocalDate.class, source -> LocalDate.parse(source, DateTimeConstant.DATE_FORMATTER));
        // Params LocalDateTime 类型入参格式化
        registry.addConverter(String.class, LocalDateTime.class, source -> LocalDateTime.parse(source, DateTimeConstant.DATE_TIME_FORMATTER));
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源映射，开发环境禁用缓存
        registry.addResourceHandler("/static/**")
            .addResourceLocations("classpath:/static/")
            .setCachePeriod(0)
            .resourceChain(false);

        // 添加根目录访问静态资源
        registry.addResourceHandler("/*.html")
            .addResourceLocations("classpath:/static/")
            .setCachePeriod(0)
            .resourceChain(false);

        registry.addResourceHandler("/*.js")
            .addResourceLocations("classpath:/static/")
            .setCachePeriod(0)
            .resourceChain(false);

        registry.addResourceHandler("/*.css")
            .addResourceLocations("classpath:/static/")
            .setCachePeriod(0)
            .resourceChain(false);
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 将根路径重定向到主页
        registry.addRedirectViewController("/", "/index.html");
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 配置CORS，允许前端跨域访问
        registry.addMapping("/playground/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true);
    }
}

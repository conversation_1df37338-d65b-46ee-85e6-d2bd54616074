package com.dangbei.molili.domain.gateway;

import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.entity.UserInfoEntity;

/**
 * UserInfo 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
public interface UserInfoGateway extends BaseGateway<Long, UserInfoEntity> {

    /**
     * 根据用户ID查询用户信息
     * @param userId 用户ID
     * @return 用户信息实体
     */
    UserInfoEntity findByUserId(String userId);

}

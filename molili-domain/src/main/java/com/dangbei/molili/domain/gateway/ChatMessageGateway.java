package com.dangbei.molili.domain.gateway;

import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.entity.ChatMessageEntity;

import java.util.List;

/**
 * ChatMessage 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
public interface ChatMessageGateway extends BaseGateway<Long, ChatMessageEntity> {

    /**
     * 查询历史消息记录
     * @param userId 用户ID
     * @param agentId 智能体ID
     * @param beforeId 查看指定位置之前的消息ID，默认为0表示不指定位置
     * @param limit 每页数量
     * @return 历史消息记录列表
     */
    List<ChatMessageEntity> findHistoryMessages(String userId, String agentId, String beforeId, Integer limit);

    void clearHistoryMessages(String userId, String agentId);

}

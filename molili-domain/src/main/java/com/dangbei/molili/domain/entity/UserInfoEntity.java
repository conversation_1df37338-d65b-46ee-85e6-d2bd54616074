package com.dangbei.molili.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.molili.domain.common.base.BaseEntity;
import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.gateway.UserInfoGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * UserInfo 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class UserInfoEntity extends BaseEntity<Long> {

    @Schema(description = "本地用户主键ID")
    private Long id;

    @Schema(description = "用户ID，全局唯一")
    private String userId;

    @Schema(description = "SteamID64，全局唯一")
    private String steamId;

    @Schema(description = "用户昵称")
    private String username;

    @Schema(description = "头像链接")
    private String avatarUrl;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "账户创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginAt;

    @Schema(description = "是否封禁")
    private Integer banned;

    @Schema(description = "封禁原因")
    private String banReason;

    @Schema(description = "扩展字段（如地区、语言等）")
    private String metadata;

    private transient UserInfoGateway userInfoGateway = SpringUtil.getBean(UserInfoGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.userInfoGateway;
    }
}

package com.dangbei.molili.domain.gateway;

import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.entity.AgentTemplateEntity;
import com.dangbei.molili.domain.entity.UserAgentEntity;

import java.util.List;

/**
 * UserAgent 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
public interface UserAgentGateway extends BaseGateway<Long, UserAgentEntity> {
    List<UserAgentEntity> listByUser(String userId);
    UserAgentEntity getOrCreate(String userId, AgentTemplateEntity agentTemplate);

}

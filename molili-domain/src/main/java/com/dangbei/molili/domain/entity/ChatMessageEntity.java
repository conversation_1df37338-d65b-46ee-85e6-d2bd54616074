package com.dangbei.molili.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.molili.client.dto.ChatMsgExt;
import com.dangbei.molili.domain.common.base.BaseEntity;
import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.gateway.ChatMessageGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * ChatMessage 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class ChatMessageEntity extends BaseEntity<Long> {

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "消息ID")
    private String msgId;

    @Schema(description = "对话ID")
    private String chatId;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "用户提问")
    private String question;

    @Schema(description = "AI回答回答")
    private String content;

    @Schema(description = "扩展信息")
    private ChatMsgExt ext;

    private transient ChatMessageGateway chatMessageGateway = SpringUtil.getBean(ChatMessageGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.chatMessageGateway;
    }
}

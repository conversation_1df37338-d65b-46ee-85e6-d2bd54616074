package com.dangbei.molili.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.molili.domain.common.base.BaseEntity;
import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.gateway.AgentTemplateGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * AgentTemplate 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class AgentTemplateEntity extends BaseEntity<Long> {

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "智能体唯一ID")
    private String agentId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "自然属性设定（只读）")
    private String naturalAttributes;

    @Schema(description = "气质人格设定（只读）")
    private String temperamentProfile;

    @Schema(description = "交互设定（可变）")
    private String interactionProfile;

    @Schema(description = "特质人格设定（可变）")
    private String traitProfile;

    @Schema(description = "系统提示词")
    private String systemPrompt;

    @Schema(description = "用户提示词")
    private String userPrompt;

    @Schema(description = "对话模型")
    private String chatModel;

    private transient AgentTemplateGateway agentTemplateGateway = SpringUtil.getBean(AgentTemplateGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.agentTemplateGateway;
    }
}

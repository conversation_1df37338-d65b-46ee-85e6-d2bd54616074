package com.dangbei.molili.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.molili.domain.common.base.BaseEntity;
import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.gateway.ToolAgentGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * ToolAgent 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class ToolAgentEntity extends BaseEntity<Long> {

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "Agent唯一ID")
    private String agentId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "系统提示词")
    private String systemPrompt;

    @Schema(description = "用户提示词")
    private String userPrompt;

    @Schema(description = "聊天模型")
    private String chatModel;

    @Schema(description = "输出变量")
    private String outputVariable;

    @Schema(description = "输出变量类型 1-字符串 2-JSON格式 3-MD格式")
    private Integer outputVariableType;

    private transient ToolAgentGateway toolAgentGateway = SpringUtil.getBean(ToolAgentGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.toolAgentGateway;
    }
}

package com.dangbei.molili.domain.gateway;

import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.entity.AgentTemplateEntity;

import java.util.List;

/**
 * AgentTemplate 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
public interface AgentTemplateGateway extends BaseGateway<Long, AgentTemplateEntity> {

    List<AgentTemplateEntity> findAll();

    AgentTemplateEntity findByAgentId(String agentId);

}

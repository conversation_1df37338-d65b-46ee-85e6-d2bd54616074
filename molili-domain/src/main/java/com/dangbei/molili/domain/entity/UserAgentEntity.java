package com.dangbei.molili.domain.entity;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.molili.domain.common.base.BaseEntity;
import com.dangbei.molili.domain.common.base.BaseGateway;
import com.dangbei.molili.domain.gateway.UserAgentGateway;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * UserAgent 领域对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(doNotUseGetters = true)
public class UserAgentEntity extends BaseEntity<Long> {

    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "智能体唯一ID")
    private String agentId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "用户-交互设定")
    private String interactionProfile;

    @Schema(description = "用户-特质人格设定")
    private String traitProfile;

    private transient UserAgentGateway userAgentGateway = SpringUtil.getBean(UserAgentGateway.class);

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }

    @Override
    protected BaseGateway getGateWay() {
        return this.userAgentGateway;
    }
}

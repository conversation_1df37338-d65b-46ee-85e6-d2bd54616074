package com.dangbei.molili.domain.common.base;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 网关接口基类
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
public interface BaseGateway<I extends Serializable, Entity extends BaseEntity<I>> {

    /**
     * 根据 id 查询
     * @param id 主键
     * @return {@link Entity }
     */
    Entity loadById(I id);

    /**
     * 根据 id 集合查询
     * @param ids 主键集合
     * @return {@link List }<{@link Entity }>
     */
    List<Entity> loadByIds(Collection<I> ids);

    /**
     * 保存
     * @param entity 实体
     * @return {@link I } 主键
     */
    I insert(Entity entity);

    /**
     * 批量保存
     * @param entities 实体集合
     * @return boolean
     */
    boolean insertBatch(Collection<Entity> entities);

    /**
     * 根据 id 更新
     * @param entity 实体
     * @return boolean
     */
    boolean update(Entity entity);

    /**
     * 根据 id 批量更新
     * @param entities 实体集合
     * @return boolean
     */
    boolean updateBatch(Collection<Entity> entities);

    /**
     * 保存或更新
     * @param entity 实体
     * @return {@link I } 主键
     */
    I insertOrUpdate(Entity entity);

    /**
     * 批量保存或更新
     * @param entities 实体集合
     * @return boolean
     */
    boolean insertOrUpdateBatch(Collection<Entity> entities);

    /**
     * 根据 id 删除
     * @param id 主键
     * @return boolean
     */
    boolean deleteById(I id);

    /**
     * 根据 id 集合批量删除
     * @param ids 主键集合
     * @return boolean
     */
    boolean deleteBatchByIds(Collection<I> ids);

}

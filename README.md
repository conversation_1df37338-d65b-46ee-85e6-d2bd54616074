# COLA 项目简介

## 项目概述

COLA（Clean Object-Oriented and Layered Architecture）全称为整洁面向对象分层架构，它融合了分层架构、SOLID设计原则、DDD（领域驱动设计）
和CQRS（命令查询职责分离）等设计思想，形成一套可落地的应用架构。

## 项目地址

- GitHub: [alibaba/COLA](https://github.com/alibaba/COLA)
- Gitee: [jasonsang/COLA](https://gitee.com/jasonsang/COLA)

## 架构层次说明

COLA架构通过以下层次的划分，确保了系统的高内聚和低耦合，每一层都承担着特定的职责：

1. **适配层（Adapter Layer）**：
    - 负责对前端展示（web，wireless，wap）的路由和适配，对于传统B/S系统而言，adapter就相当于MVC中的`controller`。

2. **应用层（Application Layer）**：
    - 负责获取输入，组装上下文，参数校验，调用领域层做业务处理。
    - 对于查询视图操作，应用层也可以绕过领域层，直接访问基础实施层。

3. **领域层（Domain Layer）**：
    - 核心业务逻辑应由领域层实现，或者逐渐由应用层的`executor`下沉到领域层。
    - 通过~~领域服务（Domain Service）和~~领域对象（Domain Entity）来提供业务实体和业务逻辑实现。

4. **基础设施层（Infrastructure Layer）**：
    - 提供技术实现的细节，如数据库的`CRUD`操作、搜索引擎、分布式服务的`RPC`等。
    - 实现领域层与外部依赖（如数据库、消息队列等）的解耦。

5. **客户端层（Client Layer）**：
    - 定义服务对外透出的API，可作为RPC SDK以`JAR`包的形式提供接口。
    - 定义服务对外的DTO，包括入参`cmd`、`query`及出参`Client Object`。

![module-app.png](docs/images/module-app.png)
![module-domain.png](docs/images/module-domain.png)
![module-infra.png](docs/images/module-infra.png)

## 架构组件图

![component.png](docs/images/component.png)

## 架构时序图

![sequence.png](docs/images/sequence.png)

## 修改点

本项目基于COLA官方 5.0.0-SNAPSHOT 脚手架，并进行了以下修改：

1. **包路径增强**：加入了模块名称以提升可读性。
2. **项目标识**：在`start`模块中加入了`artifactId`以区分不同项目。
3. **命名规范**：领域实体类名称后加上了`Entity`后缀。
4. **出参统一**：`controller`的出参统一改为`CO`（Client Object）。
5. **入参规范**：`controller`的入参避免直接使用`CO`，而是使用具体的字段。
6. **依赖精简**：去除上层服务对下层已依赖项的重复依赖。
7. **新增公共模块**：新增了`common`模块，用于存放工具类和公共类。

## 规范说明

### 通用模块 (`common`)

- 工具类和常量应放置于新创建的`common`模块中。
  ![common.png](docs/images/common.png)

### Convertor, Validator, Assembler 命名

- COLA 3.0 中提出了去掉`Convertor`、`Validator`、`Assembler`，并不是说不需要这几个层，而是命名可以由团队自己定义，因此本项目继续沿用其命名。
  由于使用了`jakarta.validation.constraints`进行参数校验，`Validator`作用不大，因此去掉`Validator`。
  ![convertor-validator-assembler-name.png](docs/images/convertor-validator-assembler-name.png)

### 定时任务与消息队列消费者位置

- 定时任务和消息队列（MQ）的入口逻辑应放置在 Adapter 层。
  ![consumer-scheduler-place.png](docs/images/consumer-scheduler-place.png)
  ![scheduler-place.png](docs/images/scheduler-place.png)

### Redis 缓存操作

- 所有往 Redis 写入缓存数据的操作，应通过`infrastructure`层进行封装。其他层次通过调用`infrastructure`层提供的封装接口来使用缓存功能。
  ![redis-place.png](docs/images/redis-place.png)

### 聚合型 Domain Entity 持久化

- ~~聚合型领域实体（Aggregate Root）应在`App`层的`executor`中注入其他实体的`gateway`进行持久化。实体应专注于业务逻辑，避免直接涉及持久化操作。
  实体（Entity）应该保持其业务逻辑的内聚性，而持久化操作通常被视为技术细节，应该由基础设施层（Infrastructure Layer）来处理。~~
  ![aggregation-persistence.png](docs/images/aggregation-persistence.png)

~~### 领域实体由 Spring 容器管理~~

~~- 领域实体（Domain Entity）应由 Spring 容器管理，以利用 Spring 提供的依赖注入和其他特性。~~
~~![entity-spring.png](docs/images/entity-spring.png)~~

~~### 领域服务接口实现~~

~~- 若领域服务（Domain Service）以接口形式存在，应在`domainservice`包下创建`impl`包，并在该包中实现接口。~~
~~![domain-service.png](docs/images/domain-service.png)~~

### 查询封装

- 简单的主键查询可以不需要额外封装`query`对象。
- ![simple-query.png](docs/images/simple-query.png)

### 校验

#### 业务校验

- 涉及具体业务规则和逻辑的校验，通常由领域服务或领域模型自身完成。例如，检查用户是否有足够的信用分来申请贷款。

#### 技术校验

- 确保输入数据的技术正确性，通常由`App`层完成。如电话号码、邮箱格式等。

## 项目结构

```
├── cola-test-adapter
│   └── adapter
│       ├── common
│       │   ├── aspect       # AOP切面
│       │   ├── config       # web相关配置
│       │   ├── exception    # 异常相关
│       │   └── filter       # 过滤器，如dubbo ConsumerTraceFilter和dubbo ProviderTraceFilter
│       ├── consumer         # 消息消费者，处理异步消息的入口
│       ├── scheduler        # 定时任务的入口
│       └── web              # Web控制器，处理及响应HTTP请求，如RESTful API
├── cola-test-app
│   └── app
│       ├── assembler        # 转换器，用于Client层的DTO与Domain层的Entity之间的转换，对于查询视图操作，也可进行Infrastructure层的数据对象（dataobject）和Client层的DTO之间的转换
│       ├── executor         # 业务逻辑执行器，实现具体的业务逻辑
│       │   └── query        # 查询逻辑执行器，处理数据查询逻辑
│       ├── extension        # COLA 扩展点实现
│       ├── extensionpoint   # COLA 扩展点
│       ├── facade           # rpc接口实现，实现client层的facade的接口
│       │   └── aspect       # dubbo AOP切面
│       └── service          # 应用服务，实现client层的api的接口，调用executor完成业务逻辑
├── cola-test-client
│   └── client
│       ├── constant         # 常量
│       ├── dto              # 数据传输对象
│       │   ├── clientobject # 客户端对象，约等同于VO
│       │   └── cmd          # 命令对象，封装客户端命令参数
│       │       └── query    # 查询命令对象，封装查询请求参数
│       └── facade           # rpc接口，由app层的facade实现
├── cola-test-common
│   └── common
│       ├── annotation        # 注解
│       ├── constant          # 常量
│       ├── enums             # 枚举
│       └── util              # 工具类
├── cola-test-domain
│   └── domain
│       ├── common            # 通用类
│       │   └── base          # 领域模型基类或基础接口
│       ├── entity            # 领域实体，包含业务属性和行为
│       └── gateway           # 领域网关，领域模型与外部依赖交互，使用依赖倒置，反转了Domain层和Infrastructure层的依赖关系，使Domain层摆脱对技术细节的依赖
├── cola-test-infrastructure
│   └── src
│       └── main
│           ├── java
│           │   └── com
│           │       └── dangbei
│           │           └── colatest
│           │               └── infrastructure
│           │                   ├── common
│           │                   │   ├── base            # 基础设施的基类或基础接口
│           │                   │   ├── handler         # 处理器，如Mybatis Plus自动填充处理
│           │                   │   └── util            # 基础设施层的工具类
│           │                   ├── config              # 配置相关的类，如 Mybatis Plus 配置
│           │                   │   └── properties      # 配置属性，@ConfigurationProperties
│           │                   ├── convertor           # 转换器，实现数据对象（dataobject）与领域实体（entity）之间的互转
│           │                   ├── gatewayimpl         # 领域网关的技术实现
│           │                   │   └── database        # 数据库访问实现
│           │                   │       ├── dataobject  # 数据库的数据对象，对应数据库中的表
│           │                   │       └── mapper      # 数据库访问对象映射
│           │                   └── repository          # 仓库接口
│           │                       └── impl            # 仓库接口实现
│           └── resources
│               └── mapper  # 数据库操作对应的XML
├── cola-test-starter
│   └── src
│       └── main
│           ├── java
│           │   └── com
│           │       └── dangbei
│           │           └── colatest
│           │               └── starter # 启动类
│           └── resources
│               └── META-INF
│                   └── dubbo # dubbo spi 扩展点配置
└── docs
    ├── images # 图片
    └── uml    # UML
```

## 参考

- [COLA GitHub Repo](https://github.com/alibaba/COLA)
- [COLA 4.0：应用架构的最佳实践](https://blog.csdn.net/significantfrank/article/details/110934799)
- [应用架构COLA 3.1：分类思维](https://blog.csdn.net/significantfrank/article/details/109529311)
- [应用架构COLA3.0：让事情回归简单](https://blog.csdn.net/significantfrank/article/details/106976804)
- [应用架构COLA 2.0](https://blog.csdn.net/significantfrank/article/details/100074716)
- [复杂性应对之道 - 领域建模](https://blog.csdn.net/significantfrank/article/details/79614915)
- [smile-cola GitHub Repo](https://github.com/charles0719/smile-cola)
- [eden-demo-cola GitHub Repo](https://github.com/shiyindaxiaojie/eden-demo-cola)
- [think-cola GitHub Repo](https://github.com/AmosWang0626/think-cola)

## 其它

[Java 8 LocalDateTime API 使用指南](docs/LocalDateTime-guide.md)

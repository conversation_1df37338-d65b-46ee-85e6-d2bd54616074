FROM db-harbor-registry.cn-shenzhen.cr.aliyuncs.com/java_app/openjdk:17.0.2-slim-with-front

EXPOSE 80
VOLUME /tmp
ENV TZ=Asia/Shanghai

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

ARG JAR_FILE

ADD docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh && ln -s /usr/local/bin/docker-entrypoint.sh /entrypoint.sh
ADD ${JAR_FILE} /app.jar
ENTRYPOINT ["docker-entrypoint.sh"]

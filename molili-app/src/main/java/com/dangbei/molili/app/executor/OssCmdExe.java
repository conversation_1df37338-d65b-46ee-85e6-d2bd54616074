package com.dangbei.molili.app.executor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.dangbei.molili.client.dto.clientobject.OssStsCo;
import com.dangbei.molili.infrastructure.config.properties.OssProperties;
import com.dangbei.framework.insight.redis.util.RedisCacheUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.time.Instant;
import java.util.Date;

/**
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Component
@Slf4j
public class OssCmdExe {

    @Resource
    private OssProperties ossProperties;

    @Resource
    private OSS ossClient;

    public SingleResponse<OssStsCo> sts() {
        String cacheKey = "oss_sts_key";
        AssumeRoleResponse response = RedisCacheUtil.getObject(() -> {
            DefaultProfile.addEndpoint(ossProperties.getRegionId(), "Sts", ossProperties.getEndpoint());
            IClientProfile profile = DefaultProfile.getProfile(ossProperties.getRegionId(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            DefaultAcsClient client = new DefaultAcsClient(profile);
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setSysMethod(MethodType.POST);
            request.setRoleArn(ossProperties.getRoleArn());
            request.setRoleSessionName(ossProperties.getRoleSessionName());
            request.setPolicy(ossProperties.getPolicy());
            request.setDurationSeconds(ossProperties.getDurationSeconds());
            try {
                return client.getAcsResponse(request);
            } catch (Exception e) {
                return null;
            }
        }, AssumeRoleResponse.class, cacheKey, ossProperties.getCacheSeconds());

        Assert.notNull(response, "获取oss sts失败");
        OssStsCo ossStsCo = new OssStsCo();
        AssumeRoleResponse.Credentials credentials = response.getCredentials();
        ossStsCo.setAccessKeyId(credentials.getAccessKeyId());
        ossStsCo.setAccessKeySecret(credentials.getAccessKeySecret());
        ossStsCo.setSecurityToken(credentials.getSecurityToken());
        ossStsCo.setRegionId(ossProperties.getRegionId());
        ossStsCo.setBucket(ossProperties.getBucket());
        ossStsCo.setUploadPath(createFilePath());
        ossStsCo.setOssEndpoint(ossProperties.getOssEndpoint());
        ossStsCo.setExpiration(Instant.parse(credentials.getExpiration()).toEpochMilli());
        ossStsCo.setDomainName(ossProperties.getDomainName());
        ossStsCo.setFileName(createRandomObjectStorageFileName());
        return SingleResponse.of(ossStsCo);
    }

    /**
     * 生成预签名URL
     * @param objectName Object完整路径，不包含 bucket 名称
     * @return 预签名URL
     */
    public URL generatePresignedUrl(String objectName) {
        var bucket = ossProperties.getBucket();
        // 默认一小时过期
        Date expiration = new Date(new Date().getTime() + ossProperties.getPreSignExpiration());
        return ossClient.generatePresignedUrl(bucket, objectName, expiration);
    }

    /**
     * 获取文件元数据
     * @param objectName Object完整路径，不包含 bucket 名称
     * @return 文件元数据
     */
    public SimplifiedObjectMeta getSimplifiedObjectMeta(String objectName) {
        var bucket = ossProperties.getBucket();
        try {
            return ossClient.getSimplifiedObjectMeta(bucket, objectName);
        } catch (Exception e) {
            log.warn("获取文件元数据失败", e);
            return null;
        }
    }

    public static String createRandomObjectStorageFileName() {
        return "_" + System.currentTimeMillis()+ "_" + RandomUtil.randomString(8);
    }

    private String createFilePath() {
        // 设置文件存储路径
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long id = snowflake.nextId();
        return StringUtils.join(DateUtil.format(new Date(), "yyyy/MM/dd"), "/", id);
    }
}

// package com.dangbei.molili.app.facade.aspect;
//
// import com.alibaba.cola.common.enums.BasicErrorCode;
// import com.alibaba.cola.dto.MultiResponse;
// import com.alibaba.cola.dto.PageResponse;
// import com.alibaba.cola.dto.PageSingleResponse;
// import com.alibaba.cola.dto.Response;
// import com.alibaba.cola.dto.SingleResponse;
// import com.alibaba.cola.exception.BizException;
// import com.alibaba.cola.exception.ValidationUtil;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.commons.lang3.StringUtils;
// import org.aspectj.lang.ProceedingJoinPoint;
// import org.aspectj.lang.annotation.Around;
// import org.aspectj.lang.annotation.Aspect;
// import org.aspectj.lang.reflect.MethodSignature;
// import org.springframework.stereotype.Component;
//
// import java.util.Objects;
//
// /**
//  * rpc 切面
//  * <AUTHOR>
//  * @version 1.0.0
//  * @since 2024/05/29
//  */
// @Aspect
// @Component
// @Slf4j
// public class FacadeAspect {
//
//     /**
//      * 是否是 COLA 的 Response
//      * @param returnType 返回类型
//      * @return boolean
//      */
//     private static <T> boolean isColaResponse(Class<T> returnType) {
//         return returnType == Response.class || returnType.getGenericSuperclass() == Response.class;
//     }
//
//     /**
//      * 如果是 COLA 的 Response，则进行异常捕获
//      * @param joinPoint 连接点
//      * @return {@link Object }
//      * @throws Throwable 异常
//      */
//     @Around(value = "@within(org.apache.dubbo.config.annotation.DubboService)")
//     public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
//         if (!(joinPoint.getSignature() instanceof MethodSignature methodSignature)) {
//             return joinPoint.proceed();
//         }
//         Class<?> returnType = methodSignature.getReturnType();
//         if (isColaResponse(returnType)) {
//             try {
//                 // 解析出参数
//                 Object[] args = joinPoint.getArgs();
//                 for (Object arg : args) {
//                     ValidationUtil.validate(arg, false);
//                 }
//                 return joinPoint.proceed();
//             } catch (Throwable e) {
//                 String errCode;
//                 String errorMsg;
//                 if (e instanceof BizException bizException) {
//                     log.warn(e.getMessage(), e);
//                     errCode = StringUtils.defaultIfBlank(bizException.getErrCode(), BasicErrorCode.DATA_FORMAT_ERROR.getErrCode());
//                     errorMsg = bizException.getMessage();
//                 } else {
//                     log.error("rpc 调用失败", e);
//                     errCode = BasicErrorCode.SYS_ERROR.getErrCode();
//                     errorMsg = "调用失败";
//                 }
//                 if (Objects.equals(returnType, Response.class)) {
//                     return Response.buildFailure(errCode, errorMsg);
//                 }
//                 if (Objects.equals(returnType, SingleResponse.class)) {
//                     return SingleResponse.buildFailure(errCode, errorMsg);
//                 }
//                 if (Objects.equals(returnType, MultiResponse.class)) {
//                     return MultiResponse.buildFailure(errCode, errorMsg);
//                 }
//                 if (Objects.equals(returnType, PageResponse.class)) {
//                     return PageResponse.buildFailure(errCode, errorMsg);
//                 }
//                 if (Objects.equals(returnType, PageSingleResponse.class)) {
//                     return PageSingleResponse.buildFailure(errCode, errorMsg);
//                 }
//                 throw e;
//             }
//         } else {
//             return joinPoint.proceed();
//         }
//     }
// }

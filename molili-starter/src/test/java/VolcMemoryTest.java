import com.alibaba.fastjson2.JSON;
import com.dangbei.molili.infrastructure.memory.request.CreateMemoryCollectionRequest;
import com.dangbei.molili.infrastructure.memory.request.GetMemoryCollectionRequest;
import com.dangbei.molili.infrastructure.memory.request.MemoryAddRequest;
import com.dangbei.molili.infrastructure.memory.request.MemorySearchRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volcengine.auth.ISignerV4;
import com.volcengine.auth.impl.SignerV4Impl;
import com.volcengine.model.Credentials;
import com.volcengine.service.SignableRequest;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import java.net.URI;
import java.util.Arrays;
import java.util.List;

/**
 * 火山记忆库
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
public class VolcMemoryTest {

    public static final String HOST = "api-knowledgebase.mlp.cn-beijing.volces.com";
    public static final String AK = "AKLTMzEyNWYwYjZkYzI0NGQxZThkYWExODJkZmE5OTFlNjE";
    public static final String SK = "TlRWbFl6QmpZall3WTJJNE5HSTNNbUZrT0Rrek9UQXdObVpoT0RReFpEZw==";

    @Test
    public void testCreate() throws Exception {
        CreateMemoryCollectionRequest request = new CreateMemoryCollectionRequest();
        request.setCollectionName("MOLILI_MEMORY_TEST");
        request.setDescription("Molili记忆库测试环境");
        request.setBuiltinEventTypes(List.of("sys_profile_collect_v1"));
        request.setBuiltinEntityTypes(List.of("sys_profile_v1"));

        String result = createCollection(JSON.toJSONString(request));
        System.out.println(result);
    }

    @Test
    public void testInfo() throws Exception {
        GetMemoryCollectionRequest request = new GetMemoryCollectionRequest();
        request.setCollectionName("MOLILI_MEMORY_TEST");

        String result = collectionInfo(JSON.toJSONString(request));
        System.out.println(result);
    }

    @Test
    public void testAddMemory() throws Exception {
        MemoryAddRequest request = new MemoryAddRequest();
        request.setCollectionName("MOLILI_MEMORY_TEST");
        request.setSessionId(System.currentTimeMillis() + "");
        request.setMessages(Arrays.asList(
            new MemoryAddRequest.Message()
                .setRole("user")
                .setContent("我下个月想去北京玩，有什么推荐的美食吗")
                .setRoleId("yyt")
                .setRoleName("yyt")
                .setTime(System.currentTimeMillis()),
            new MemoryAddRequest.Message()
                .setRole("assistant")
                .setContent("北京烤鸭、老北京涮羊肉、炸酱面")
                .setRoleId("A9636879")
                .setRoleName("A9636879")
                .setTime(System.currentTimeMillis())
        ));
        request.setMetadata(
            new MemoryAddRequest.Metadata()
                .setDefaultUserId("yyt")
                .setDefaultUserName("yyt")
                .setDefaultAssistantId("A9636879")
                .setDefaultAssistantName("A9636879")
                .setTime(System.currentTimeMillis())
        );
//        request.setEntities(
//            new MemoryAddRequest.Entities()
//                .setEntityType("sys_profile_v1")
//                .setEntityScopes(List.of(
//                    Map.of("user_profile", "yyt")
//                ))
//        );

        String response = addMessages(JSON.toJSONString(request));
        System.out.println(response);
    }

    @Test
    public void testSearchMemory() throws Exception {
        MemorySearchRequest request = new MemorySearchRequest();
        request.setCollectionName("MOLILI_MEMORY_TEST");
        request.setFilter(
            new MemorySearchRequest.Filter()
                .setUserId(List.of("yyt"))
                .setAssistantId(List.of("A9636879", "default"))
                .setMemoryType(List.of("sys_profile_v1", "sys_profile_collect_v1"))
        );

        String response = searchMemory(JSON.toJSONString(request));
        System.out.println(response);
    }

    /**
     * 创建记忆库
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    public static String createCollection(String requestJson) throws Exception {
        try {
            SignableRequest signableRequest = prepareRequest(HOST, "/api/memory/collection/create", "POST", null, requestJson, AK, SK);
            URI uri = new URIBuilder()
                .setScheme("https")
                .setHost(HOST)
                .setPath("/api/memory/collection/create")
                .build();

            HttpPost httpPost = new HttpPost(uri);
            httpPost.setConfig(signableRequest.getConfig());
            httpPost.setEntity(signableRequest.getEntity());
            for (Header header : signableRequest.getAllHeaders()) {
                httpPost.setHeader(header.getName(), header.getValue());
            }

            HttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            return responseBody;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 查询记忆库
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    public static String collectionInfo(String requestJson) throws Exception {
        try {
            SignableRequest signableRequest = prepareRequest(HOST, "/api/memory/collection/info", "POST", null, requestJson, AK, SK);
            URI uri = new URIBuilder()
                .setScheme("https")
                .setHost(HOST)
                .setPath("/api/memory/collection/info")
                .build();

            HttpPost httpPost = new HttpPost(uri);
            httpPost.setConfig(signableRequest.getConfig());
            httpPost.setEntity(signableRequest.getEntity());
            for (Header header : signableRequest.getAllHeaders()) {
                httpPost.setHeader(header.getName(), header.getValue());
            }

            HttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            return responseBody;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 上传对话
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    public static String addMessages(String requestJson) throws Exception {
        try {
            SignableRequest signableRequest = prepareRequest(HOST, "/api/memory/messages/add", "POST", null, requestJson, AK, SK);
            URI uri = new URIBuilder()
                .setScheme("https")
                .setHost(HOST)
                .setPath("/api/memory/messages/add")
                .build();

            HttpPost httpPost = new HttpPost(uri);
            httpPost.setConfig(signableRequest.getConfig());
            httpPost.setEntity(signableRequest.getEntity());
            for (Header header : signableRequest.getAllHeaders()) {
                httpPost.setHeader(header.getName(), header.getValue());
            }

            HttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            return responseBody;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 检索记忆
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    public static String searchMemory(String requestJson) throws Exception {
        try {
            SignableRequest signableRequest = prepareRequest(HOST, "/api/memory/search", "POST", null, requestJson, AK, SK);
            URI uri = new URIBuilder()
                .setScheme("https")
                .setHost(HOST)
                .setPath("/api/memory/search")
                .build();


            HttpPost httpPost = new HttpPost(uri);
            httpPost.setConfig(signableRequest.getConfig());
            httpPost.setEntity(signableRequest.getEntity());
            for (Header header : signableRequest.getAllHeaders()) {
                httpPost.setHeader(header.getName(), header.getValue());
            }

            HttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();

            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public static String toJson(Object obj) {
        try {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 将对象转换为 JSON 字符串
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static SignableRequest prepareRequest(String host, String path, String method, List<NameValuePair> params, String body, String ak, String sk) throws Exception {
        SignableRequest request = new SignableRequest();
        request.setMethod(method);
        request.setHeader("Accept", "application/json");
        request.setHeader("Content-Type", "application/json");
        request.setHeader("Host", HOST);
        request.setEntity(new StringEntity(body, "utf-8"));

        URIBuilder builder = request.getUriBuilder();
        builder.setScheme("https");
        builder.setHost(host);
        builder.setPath(path);
        if (params != null) {
            builder.setParameters(params);
        }

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(120000).setConnectTimeout(12000).build();
        request.setConfig(requestConfig);

        Credentials credentials = new Credentials("cn-north-1", "air");
        credentials.setAccessKeyID(ak);
        credentials.setSecretAccessKey(sk);

        // 签名
        ISignerV4 ISigner = new SignerV4Impl();
        ISigner.sign(request, credentials);

        return request;
    }

}

package com.dangbei.molili.starter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;
import org.springframework.util.StopWatch;
import cn.hutool.extra.spring.EnableSpringUtil;

import java.net.InetAddress;

/**
 * Spring Boot 启动类
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Slf4j
@EnableSpringUtil
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(scanBasePackages = {"com.dangbei", "com.alibaba.cola"})
@MapperScan("com.dangbei.molili.infrastructure.**.gatewayimpl.database.mapper")
public class Application {

    public static void main(String[] args) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("Begin to start Spring Boot Application");
        ApplicationContext ctx = SpringApplication.run(Application.class, args);
        Environment env = ctx.getEnvironment();
        String[] activeProfiles = env.getActiveProfiles();
        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
        String contextPath = StringUtils.defaultString(env.getProperty("server.servlet.context-path"));
        String port = StringUtils.defaultString(env.getProperty("server.port", "8080"));
        log.info(
            """
                 \n----------------------------------------------------------
                 Application '{}' is running!
                 ActiveProfile: '{}'
                 Access URLs:
                 Local: http://localhost:{}{}
                 External: http://{}:{}{}
                 Swagger : http://{}:{}{}/swagger-ui/index.html#
                 ----------------------------------------------------------
                """,
            env.getProperty("spring.application.name"),
            activeProfile,
            port, contextPath,
            InetAddress.getLocalHost().getHostAddress(), port, contextPath,
            InetAddress.getLocalHost().getHostAddress(), port, contextPath);
        stopWatch.stop();
        log.info("End starting Spring Boot Application Time used: {}ms", stopWatch.getTotalTimeMillis());
    }
}

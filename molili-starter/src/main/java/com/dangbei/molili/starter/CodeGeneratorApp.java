package com.dangbei.molili.starter;

import com.dangbei.framework.insight.code.generator.CodeGeneratorPlus;
import com.dangbei.framework.insight.code.generator.config.GeneratorConfig;

import java.util.Arrays;

/**
 * 代码生成器
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/06/28
 */
public class CodeGeneratorApp {

    public static void main(String[] args) {
        GeneratorConfig build = GeneratorConfig.builder()
            // 数据库连接信息
            .jdbcUrl("**************************************************************************************************************")
            .jdbcDriver("com.mysql.cj.jdbc.Driver")
            .jdbcUsername("molili")
            .jdbcPassword("Ai!4q4F1Ep08bt4nl")
            // 作者信息
            .author("yin.yantao")
            // 项目名
            .moduleName("molili")
            // 表名前缀（生成实体对象会过滤掉前缀）
            .tablePrefix(new String[]{
                "ai_"
            })
            // 需要生成的表名
            .tables(new String[]{
                "ai_chat_message"
            })
            // 排除文件
            .excludeFiles(Arrays.asList(

            ))
            // 表公共字段（公共字段通常会在BaseDO统一定义，生成实体对象会过滤掉）
            .commonColumns(new String[]{
                "create_time",
                "create_person",
                "update_time",
                "update_person",
                "is_deleted",
                "db_modify_timestamp"
            })
            // 包信息
            .basePackage("com.dangbei")
            .build();

        // 执行生成
        CodeGeneratorPlus.run(build);
    }
}

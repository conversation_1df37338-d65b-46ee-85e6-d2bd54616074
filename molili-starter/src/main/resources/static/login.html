<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份确认 - Molili AI</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.1.0/dist/index.iife.min.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios@1.6.2/dist/axios.min.js"></script>
    <!-- Auth Utils -->
    <script src="auth.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f6f6f6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
            border: 1px solid #eaeaea;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            margin-bottom: 32px;
        }

        .logo-container {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: #1677ff;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);
        }

        .logo-icon {
            color: white;
            font-size: 28px;
            font-weight: bold;
        }

        .login-header h1 {
            color: #121212;
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: 600;
            letter-spacing: -0.2px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
            margin: 0;
            font-weight: 400;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .input-wrapper {
            position: relative;
            min-height: 40px; /* 固定最小高度防止变大 */
        }

        .user-input {
            font-size: 14px;
        }

        .user-input .el-input__wrapper {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            border-radius: 6px;
            border: 1px solid #eaeaea;
            transition: all 0.2s ease;
        }

        .user-input .el-input__wrapper:hover {
            border-color: #1677ff;
            box-shadow: 0 2px 6px rgba(22, 119, 255, 0.1);
        }

        .user-input .el-input__wrapper.is-focus {
            border-color: #1677ff;
            box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        }

        .login-button {
            background-color: #1677ff;
            border: none;
            font-size: 14px;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
        }

        .login-button:hover {
            background-color: #0958d9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(22, 119, 255, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 4px rgba(22, 119, 255, 0.1);
        }

        .error-message {
            color: #f56c6c;
            font-size: 13px;
            margin-top: 8px;
            text-align: left;
            padding-left: 4px;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-3px); }
            75% { transform: translateX(3px); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                padding: 32px 24px;
                margin: 20px;
                max-width: none;
            }
            
            .logo-container {
                width: 50px;
                height: 50px;
                margin-bottom: 16px;
            }
            
            .logo-icon {
                font-size: 24px;
            }
            
            .login-header h1 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <div class="login-header">
                <div class="logo-container">
                    <div class="logo-icon">M</div>
                </div>
                <h1>Molili AI</h1>
                <p>欢迎回来</p>
            </div>

            <el-form @submit.native.prevent="login" class="login-form">
                <div class="input-wrapper">
                    <el-input
                        v-model="username"
                        placeholder="请输入您的用户名"
                        size="large"
                        class="user-input"
                        :prefix-icon="User"
                        @input="validateUsername"
                        clearable
                        maxlength="20"
                    />
                    <div v-if="usernameError" class="error-message">
                        {{ usernameError }}
                    </div>
                </div>
                
                <el-button
                    type="primary"
                    size="large"
                    class="login-button"
                    @click="login"
                    :loading="loading"
                    :disabled="!isUsernameValid"
                >
                    {{ loading ? '登录中...' : '立即登录' }}
                </el-button>
            </el-form>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;
        const { User } = ElementPlusIconsVue;

        createApp({
            setup() {
                const username = ref('');
                const loading = ref(false);
                const usernameError = ref('');
                const isUsernameValid = ref(false);

                // 用户名验证规则：只能包含字母、数字、下划线
                const validateUsername = () => {
                    const usernamePattern = /^[a-zA-Z0-9_]+$/;
                    const value = username.value.trim();
                    
                    if (!value) {
                        usernameError.value = '用户名不能为空';
                        isUsernameValid.value = false;
                        return;
                    }
                    
                    if (value.length < 2) {
                        usernameError.value = '用户名不能少于2个字符';
                        isUsernameValid.value = false;
                        return;
                    }
                    
                    if (value.length > 20) {
                        usernameError.value = '用户名不能超过20个字符';
                        isUsernameValid.value = false;
                        return;
                    }
                    
                    if (!usernamePattern.test(value)) {
                        usernameError.value = '用户名只能包含字母、数字、下划线';
                        isUsernameValid.value = false;
                        return;
                    }
                    
                    usernameError.value = '';
                    isUsernameValid.value = true;
                };

                // 检查是否已经登录
                const checkLogin = () => {
                    if (AuthUtils.isLoggedIn()) {
                        // 已登录，跳转到首页
                        window.location.href = 'index.html';
                    }
                };

                // 登录函数
                const login = async () => {
                    // 防止重复提交
                    if (loading.value) {
                        return;
                    }
                    
                    // 先执行用户名验证
                    validateUsername();
                    
                    if (!isUsernameValid.value) {
                        ElMessage.warning('请输入正确的用户名');
                        return;
                    }

                    loading.value = true;
                    
                    try {
                        // 调用后端登录接口
                        const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/login'), {
                            username: username.value.trim()
                        });
                        
                        if (response.data.success) {
                            // 保存用户名到Cookie
                            AuthUtils.loginUser(username.value.trim());
                            // 同时设置用户ID的Cookie
                            AuthUtils.setUserId(username.value.trim());
                            
                            ElMessage.success('登录成功，正在跳转...');
                            
                            // 延迟跳转，让用户看到成功消息
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        } else {
                            ElMessage.error(response.data.errMessage || '登录失败');
                        }
                        
                    } catch (error) {
                        console.error('登录失败:', error);
                        ElMessage.error(error.response?.data?.errMessage || '登录失败，请重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 页面加载时检查登录状态
                checkLogin();

                return {
                    username,
                    loading,
                    usernameError,
                    isUsernameValid,
                    validateUsername,
                    login,
                    User
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html> 
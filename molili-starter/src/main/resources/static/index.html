<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>li AI 功能调试后台</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.1.0/dist/index.iife.min.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios@1.6.2/dist/axios.min.js"></script>
    <!-- <PERSON><PERSON> OSS SDK -->
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js"></script>
    <!-- Auth Utils -->
    <script src="auth.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
            background: #f6f6f6;
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
            transition: all 0.2s ease-out;
        }
        
        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #ffffff;
            border-bottom: 1px solid #eaeaea;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            z-index: 1000;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .sidebar-toggle {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 16px;
            color: #3b82f6;
        }
        
        .sidebar-toggle:hover {
            background: rgba(59, 130, 246, 0.1);
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #121212;
            letter-spacing: -0.2px;
        }
        
        .header-subtitle {
            padding: 4px 8px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
            font-size: 12px;
            color: #3b82f6;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .user-card {
            display: flex;
            align-items: center;
            border-radius: 4px;
            padding: 6px 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: 1px solid transparent;
        }
        
        .user-card:hover {
            background: #f5f5f5;
            border-color: #eaeaea;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            font-size: 14px;
            margin-right: 10px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
            margin-right: 12px;
        }
        
        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            line-height: 1.2;
        }
        
        .user-role {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }
        
        .user-menu-btn {
            background: none;
            border: none;
            font-size: 14px;
            color: #999;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .user-menu-btn:hover {
            color: #3b82f6;
        }
        
        /* Sidebar */
        .sidebar {
            width: 220px;
            background: #fff;
            border-right: 1px solid #eaeaea;
            margin-top: 56px;
            height: calc(100vh - 56px);
            overflow-y: auto;
            transition: all 0.2s ease-out;
            position: fixed;
            left: 0;
            z-index: 100;
        }
        
        .sidebar.collapsed {
            width: 60px;
        }
        
        .sidebar-content {
            padding: 12px 0;
        }
        
        .menu-section {
            margin-bottom: 16px;
        }
        
        .menu-section-title {
            font-size: 12px;
            font-weight: 500;
            color: #999;
            padding: 8px 16px;
            transition: opacity 0.2s ease;
        }
        
        .sidebar.collapsed .menu-section-title {
            opacity: 0;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            margin: 2px 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease-out;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            color: #333;
        }
        
        .menu-item:hover {
            background: #f5f5f5;
        }
        
        .menu-item.active {
            background: #e6f4ff;
            color: #3b82f6;
            font-weight: 500;
        }
        
        .menu-icon {
            width: 18px;
            height: 18px;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            transition: all 0.2s ease;
        }
        
        .sidebar.collapsed .menu-icon {
            margin-right: 0;
        }
        
        .menu-text {
            font-size: 14px;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .sidebar.collapsed .menu-text {
            opacity: 0;
            transform: translateX(-10px);
        }
        
        .menu-badge {
            margin-left: auto;
            background: #3b82f6;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
        }
        
        .sidebar.collapsed .menu-badge {
            display: none;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 220px;
            margin-top: 56px;
            padding: 24px;
            transition: all 0.2s ease-out;
        }
        
        .sidebar.collapsed + .main-content {
            margin-left: 60px;
        }
        
        .content-header {
            margin-bottom: 24px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 13px;
            color: #999;
        }
        
        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #121212;
        }
        
        .content-subtitle {
            font-size: 14px;
            color: #666;
            font-weight: 400;
            line-height: 1.5;
        }
        
        /* Agent Cards Grid */
        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }
        
        .agent-card {
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.2s ease-out;
            border: 1px solid #eaeaea;
            position: relative;
        }
        
        .agent-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: #d9d9d9;
        }
        
        .agent-card:hover .agent-actions {
            opacity: 1;
        }
        
        .agent-actions {
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: all 0.2s ease;
        }
        
        .agent-action-btn {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            border: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #64748b;
            transition: all 0.2s ease;
        }
        
        .agent-action-btn:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
            transform: translateY(-1px);
        }
        
        .agent-action-btn.copy-btn:hover {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .agent-action-btn.delete-btn:hover {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .agent-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .agent-avatar {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            margin-right: 16px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .agent-info {
            flex: 1;
        }
        
        .agent-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #121212;
            line-height: 1.3;
        }
        
        .agent-id {
            font-size: 12px;
            color: #999;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
            font-weight: 500;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        
        .agent-description {
            margin: 16px 0;
            line-height: 1.6;
            color: #666;
            font-size: 14px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .agent-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .agent-tag {
            background: #f0f7ff;
            color: #3b82f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid #d4e8ff;
            transition: all 0.2s ease;
        }
        
        .agent-card:hover .agent-tag {
            background: #e6f4ff;
            border-color: #bfdbfe;
        }
        

        
        /* Loading & Empty States */
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(59, 130, 246, 0.1);
            border-left: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        .loading-text {
            font-size: 14px;
            color: #666;
            font-weight: 400;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            padding: 32px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #999;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .empty-description {
            font-size: 14px;
            color: #666;
            text-align: center;
            max-width: 400px;
            line-height: 1.5;
        }
        
        .welcome-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            padding: 32px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }
        
        .welcome-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #3b82f6;
        }
        
        .welcome-title {
            font-size: 20px;
            font-weight: 600;
            color: #121212;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .welcome-subtitle {
            font-size: 14px;
            color: #666;
            text-align: center;
            max-width: 500px;
            line-height: 1.6;
        }
        
        /* Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Custom Scrollbar */
        .sidebar::-webkit-scrollbar,
        .main-content::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track,
        .main-content::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .sidebar::-webkit-scrollbar-thumb,
        .main-content::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }
        
        .sidebar::-webkit-scrollbar-thumb:hover,
        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
        }
        
        /* Copy Dialog Styles */
        .copy-dialog-content {
            padding: 8px 0;
        }
        
        .copy-source-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
        }
        
        .source-agent-card {
            display: flex;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .source-agent-card .agent-avatar {
            width: 40px;
            height: 40px;
            font-size: 16px;
            margin-right: 12px;
        }
        
        .copy-form {
            padding: 0 4px;
        }
        
        .copy-form .el-form-item {
            margin-bottom: 20px;
        }
        
        .copy-form .el-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .copy-form .el-checkbox {
            margin-right: 0;
        }
        
        .copy-options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .copy-options-grid .el-checkbox {
            margin-right: 0;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            transition: all 0.2s ease;
        }
        
        .copy-options-grid .el-checkbox:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        
        .copy-options-grid .el-checkbox.is-checked {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .copy-options-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        /* 删除确认对话框样式 */
        .delete-confirm-dialog {
            border-radius: 12px;
        }
        
        .delete-confirm-dialog .el-message-box__title {
            color: #ef4444;
            font-weight: 600;
        }
        
        .delete-confirm-dialog .el-message-box__content {
            padding: 20px 24px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -220px;
                z-index: 999;
            }
            
            .sidebar.open {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
                padding: 16px;
            }
            
            .agent-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 0 16px;
            }
            
            .user-card {
                padding: 4px 8px;
            }
            
            .user-info {
                display: none;
            }
            
            .el-dialog {
                width: 90vw !important;
                margin: 0 auto;
            }
            
            .agent-actions {
                top: 8px;
                right: 8px;
                gap: 6px;
            }
            
            .agent-action-btn {
                width: 28px;
                height: 28px;
            }
            
            .agent-action-btn svg {
                width: 14px;
                height: 14px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <button class="sidebar-toggle" @click="toggleSidebar">
                        <i class="el-icon-menu"></i>
                    </button>
                    <h1 class="header-title">Molili AI</h1>
                    <span class="header-subtitle">功能调试后台</span>
                </div>
                
                <div class="user-card" @click="showUserMenu">
                    <div class="user-avatar">{{ currentUser.charAt(0).toUpperCase() }}</div>
                    <div class="user-info">
                        <div class="user-name">{{ currentUser }}</div>
                        <div class="user-role">管理员</div>
                    </div>
                    <button class="user-menu-btn">
                        <i class="el-icon-more"></i>
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
                <div class="sidebar-content">
                    <div class="menu-section">
                        <div class="menu-section-title">主要功能</div>
                        <div class="menu-item" :class="{ active: currentMenu === 'dashboard' }" @click="selectMenu('dashboard')">
                            <div class="menu-icon">📊</div>
                            <div class="menu-text">仪表盘</div>
                        </div>
                        <div class="menu-item" :class="{ active: currentMenu === 'roleplay' }" @click="selectMenu('roleplay')">
                            <div class="menu-icon">🎭</div>
                            <div class="menu-text">角色扮演</div>
                            <div class="menu-badge">{{ agentTemplates.length }}</div>
                        </div>
                        <div class="menu-item" :class="{ active: currentMenu === 'chat' }" @click="selectMenu('chat')">
                            <div class="menu-icon">💬</div>
                            <div class="menu-text">智能对话</div>
                        </div>
                        <div class="menu-item" :class="{ active: currentMenu === 'knowledge' }" @click="selectMenu('knowledge')">
                            <div class="menu-icon">📚</div>
                            <div class="menu-text">知识库</div>
                        </div>
                    </div>
                    
                    <div class="menu-section">
                        <div class="menu-section-title">管理工具</div>
                        <div class="menu-item" :class="{ active: currentMenu === 'users' }" @click="selectMenu('users')">
                            <div class="menu-icon">👥</div>
                            <div class="menu-text">用户管理</div>
                        </div>
                        <div class="menu-item" :class="{ active: currentMenu === 'analytics' }" @click="selectMenu('analytics')">
                            <div class="menu-icon">📈</div>
                            <div class="menu-text">数据分析</div>
                        </div>
                        <div class="menu-item" :class="{ active: currentMenu === 'settings' }" @click="selectMenu('settings')">
                            <div class="menu-icon">⚙️</div>
                            <div class="menu-text">系统设置</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- 角色扮演内容 -->
                <div v-if="currentMenu === 'roleplay'">
                    <div class="content-header">
                        <div class="breadcrumb">
                            <div class="breadcrumb-item">
                                <i class="el-icon-house"></i>
                                <span>首页</span>
                            </div>
                            <i class="el-icon-arrow-right"></i>
                            <div class="breadcrumb-item">
                                <span>角色扮演</span>
                            </div>
                        </div>
                        <h1 class="content-title">AI 角色扮演</h1>
                        <p class="content-subtitle">选择一个AI角色开始精彩的对话体验，每个角色都有独特的个性和专业能力</p>
                    </div>

                    <div v-if="loading" class="loading-state">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">正在加载角色数据...</div>
                    </div>

                    <div v-else-if="agentTemplates.length === 0" class="empty-state">
                        <div class="empty-icon">🤖</div>
                        <h3 class="empty-title">暂无AI角色</h3>
                        <p class="empty-description">还没有创建任何AI角色，请联系管理员添加</p>
                    </div>

                    <div v-else class="agent-grid">
                        <div v-for="agent in agentTemplates" :key="agent.id" class="agent-card" @click="selectAgent(agent)">
                            <div class="agent-actions">
                                <el-tooltip content="复制智能体" placement="top">
                                    <button class="agent-action-btn copy-btn" @click.stop="copyAgent(agent)">
                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                            <path d="M16 1H4C2.9 1 2 1.9 2 3v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                        </svg>
                                    </button>
                                </el-tooltip>
                                <el-tooltip content="删除智能体" placement="top">
                                    <button class="agent-action-btn delete-btn" @click.stop="deleteAgent(agent)">
                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                        </svg>
                                    </button>
                                </el-tooltip>
                            </div>
                            <div class="agent-header">
                                <div class="agent-avatar">
                                    {{ agent.name ? agent.name.charAt(0) : 'A' }}
                                </div>
                                <div class="agent-info">
                                    <h3 class="agent-name">{{ agent.name || '未命名角色' }}</h3>
                                    <div class="agent-id">{{ agent.agentId }}</div>
                                </div>
                            </div>
                            <div class="agent-description">
                                这是一个智能AI助手，具备丰富的知识储备和优秀的对话能力，能够为您提供专业的服务和支持。
                            </div>
                            <div class="agent-tags">
                                <div v-if="agent.naturalAttributes" class="agent-tag">🌟 自然属性</div>
                                <div v-if="agent.temperamentProfile" class="agent-tag">🎨 气质人格</div>
                                <div v-if="agent.interactionProfile" class="agent-tag">💡 交互设定</div>
                                <div v-if="agent.traitProfile" class="agent-tag">⚡ 特质人格</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他页面的默认欢迎内容 -->
                <div v-else class="welcome-state">
                    <div class="welcome-icon">👋</div>
                    <h2 class="welcome-title">欢迎使用 Molili AI 功能调试后台</h2>
                    <p class="welcome-subtitle">这里是您的AI助手调试中心，从左侧菜单选择功能开始体验吧！</p>
                </div>
            </div>
        </div>

        <!-- 复制智能体对话框 -->
        <el-dialog 
            v-model="copyDialogVisible" 
            title="复制智能体" 
            width="600px"
            :before-close="handleCloseCopyDialog"
            center>
            <div class="copy-dialog-content">
                <div class="copy-source-info">
                    <h4 style="margin-bottom: 12px; color: #666;">复制来源</h4>
                    <div class="source-agent-card">
                        <div class="agent-avatar">
                            {{ sourceAgent.name ? sourceAgent.name.charAt(0) : 'A' }}
                        </div>
                        <div class="agent-info">
                            <h3 class="agent-name">{{ sourceAgent.name || '未命名角色' }}</h3>
                            <div class="agent-id">{{ sourceAgent.agentId }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="copy-form" style="margin-top: 24px;">
                    <h4 style="margin-bottom: 16px; color: #666;">新智能体信息</h4>
                    <el-form :model="copyForm" label-width="120px" label-position="left">
                        <el-form-item label="智能体名称" required>
                            <el-input 
                                v-model="copyForm.name" 
                                placeholder="请输入新的智能体名称"
                                :maxlength="50"
                                show-word-limit
                                clearable>
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item label="复制内容">
                            <div class="copy-options-grid">
                                <el-checkbox v-model="copyForm.copyNaturalAttributes">
                                    🌟 自然属性
                                </el-checkbox>
                                <el-checkbox v-model="copyForm.copyTemperamentProfile">
                                    🎨 气质人格
                                </el-checkbox>
                                <el-checkbox v-model="copyForm.copyInteractionProfile">
                                    💡 交互设定
                                </el-checkbox>
                                <el-checkbox v-model="copyForm.copyTraitProfile">
                                    ⚡ 特质人格
                                </el-checkbox>
                                <el-checkbox v-model="copyForm.copyChatModel">
                                    🤖 模型配置
                                </el-checkbox>
                                <el-checkbox v-model="copyForm.copySystemPrompt">
                                    🔧 系统提示词
                                </el-checkbox>
                                <el-checkbox v-model="copyForm.copyUserPrompt">
                                    👤 用户提示词
                                </el-checkbox>
                            </div>
                        </el-form-item>
                        
                        <el-form-item>
                            <div class="copy-options-actions">
                                <el-button size="small" @click="selectAllCopyOptions">全选</el-button>
                                <el-button size="small" @click="clearAllCopyOptions">清空</el-button>
                                <el-button size="small" @click="selectBasicCopyOptions">基础选项</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="copyDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmCopy" :loading="copyLoading">
                        {{ copyLoading ? '复制中...' : '确认复制' }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                const currentUser = ref('YYT');
                const currentMenu = ref('');
                const agentTemplates = ref([]);
                const loading = ref(false);
                const sidebarCollapsed = ref(false);
                
                // 复制相关状态
                const copyDialogVisible = ref(false);
                const copyLoading = ref(false);
                const sourceAgent = ref({});
                const copyForm = ref({
                    name: '',
                    copyNaturalAttributes: true,
                    copyTemperamentProfile: true,
                    copyInteractionProfile: true,
                    copyTraitProfile: true,
                    copyChatModel: true,
                    copySystemPrompt: true,
                    copyUserPrompt: true
                });

                // 配置axios
                axios.defaults.withCredentials = true;
                axios.defaults.headers.common['Content-Type'] = 'application/json';

                // 检查登录状态
                const checkAuth = () => {
                    if (!AuthUtils.requireAuth()) {
                        return false;
                    }
                    currentUser.value = AuthUtils.getCurrentUser() || 'YYT';
                    return true;
                };

                // 切换侧边栏
                const toggleSidebar = () => {
                    sidebarCollapsed.value = !sidebarCollapsed.value;
                };

                // 显示用户菜单
                const showUserMenu = () => {
                    ElMessageBox.confirm('确定要退出登录吗？', '用户菜单', {
                        confirmButtonText: '退出登录',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        logout();
                    }).catch(() => {
                        // 取消操作
                    });
                };

                // 选择菜单
                const selectMenu = (menuKey) => {
                    currentMenu.value = menuKey;
                    if (menuKey === 'roleplay') {
                        loadAgentTemplates();
                    }
                };

                // 加载Agent模板列表
                const loadAgentTemplates = async () => {
                    loading.value = true;
                    try {
                        const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-templates'), {});

                        if (response.data && response.data.success) {
                            agentTemplates.value = response.data.data || [];
                            console.log('Agent模板加载成功:', agentTemplates.value);
                        } else {
                            throw new Error(response.data?.errMessage || '获取数据失败');
                        }
                    } catch (error) {
                        console.error('加载Agent模板失败:', error);
                        ElMessage.error('加载角色数据失败: ' + (error.response?.data?.errMessage || error.message));
                        agentTemplates.value = [];
                    } finally {
                        loading.value = false;
                    }
                };

                // 选择Agent
                const selectAgent = (agent) => {
                    console.log('选择了Agent:', agent);
                    ElMessage.success(`正在为您打开 ${agent.name} 的聊天页面...`);
                    
                    // 新窗口打开chat.html页面，只传递agentId参数
                    window.open(`chat.html?agentId=${agent.agentId}`, '_blank');
                };

                // 复制Agent
                const copyAgent = (agent) => {
                    console.log('复制Agent:', agent);
                    sourceAgent.value = { ...agent };
                    
                    // 重置表单
                    copyForm.value = {
                        name: `${agent.name || '未命名角色'} - 副本`,
                        copyNaturalAttributes: true,
                        copyTemperamentProfile: true,
                        copyInteractionProfile: true,
                        copyTraitProfile: true,
                        copyChatModel: true,
                        copySystemPrompt: true,
                        copyUserPrompt: true
                    };
                    
                    copyDialogVisible.value = true;
                };

                // 全选复制选项
                const selectAllCopyOptions = () => {
                    copyForm.value.copyNaturalAttributes = true;
                    copyForm.value.copyTemperamentProfile = true;
                    copyForm.value.copyInteractionProfile = true;
                    copyForm.value.copyTraitProfile = true;
                    copyForm.value.copyChatModel = true;
                    copyForm.value.copySystemPrompt = true;
                    copyForm.value.copyUserPrompt = true;
                };

                // 清空复制选项
                const clearAllCopyOptions = () => {
                    copyForm.value.copyNaturalAttributes = false;
                    copyForm.value.copyTemperamentProfile = false;
                    copyForm.value.copyInteractionProfile = false;
                    copyForm.value.copyTraitProfile = false;
                    copyForm.value.copyChatModel = false;
                    copyForm.value.copySystemPrompt = false;
                    copyForm.value.copyUserPrompt = false;
                };

                // 基础选项（只选择核心属性）
                const selectBasicCopyOptions = () => {
                    copyForm.value.copyNaturalAttributes = true;
                    copyForm.value.copyTemperamentProfile = true;
                    copyForm.value.copyInteractionProfile = true;
                    copyForm.value.copyTraitProfile = true;
                    copyForm.value.copyChatModel = false;
                    copyForm.value.copySystemPrompt = false;
                    copyForm.value.copyUserPrompt = false;
                };

                // 关闭复制对话框
                const handleCloseCopyDialog = (done) => {
                    if (copyLoading.value) {
                        ElMessage.warning('复制进行中，请稍候...');
                        return;
                    }
                    done();
                };

                // 确认复制
                const confirmCopy = async () => {
                    // 验证表单
                    if (!copyForm.value.name.trim()) {
                        ElMessage.error('请输入智能体名称');
                        return;
                    }

                    copyLoading.value = true;
                    
                    try {
                        // 构造请求参数
                        const requestData = {
                            agentId: sourceAgent.value.agentId,
                            name: copyForm.value.name.trim(),
                            copyNaturalAttributes: copyForm.value.copyNaturalAttributes,
                            copyTemperamentProfile: copyForm.value.copyTemperamentProfile,
                            copyInteractionProfile: copyForm.value.copyInteractionProfile,
                            copyTraitProfile: copyForm.value.copyTraitProfile,
                            copyChatModel: copyForm.value.copyChatModel,
                            copySystemPrompt: copyForm.value.copySystemPrompt,
                            copyUserPrompt: copyForm.value.copyUserPrompt
                        };

                        console.log('复制请求参数:', requestData);

                        // 调用后端接口
                        const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-template/copy'), requestData);

                        if (response.data && response.data.success) {
                            ElMessage.success(`智能体 "${copyForm.value.name}" 复制成功！`);
                            copyDialogVisible.value = false;
                            
                            // 重新加载智能体列表
                            loadAgentTemplates();
                            
                            console.log('复制成功响应:', response.data);

                        } else {
                            throw new Error(response.data?.errMessage || '复制失败');
                        }

                    } catch (error) {
                        console.error('复制智能体失败:', error);
                        ElMessage.error('复制失败: ' + (error.response?.data?.errMessage || error.message));
                    } finally {
                        copyLoading.value = false;
                    }
                };

                // 删除智能体
                const deleteAgent = async (agent) => {
                    try {
                        await ElMessageBox.confirm(
                            `确定要删除智能体 "${agent.name}" 吗？删除后无法恢复！`,
                            '删除智能体',
                            {
                                confirmButtonText: '确定删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                                confirmButtonClass: 'el-button--danger',
                                dangerouslyUseHTMLString: false,
                                customClass: 'delete-confirm-dialog'
                            }
                        );

                        // 用户确认删除，调用后端接口
                        const response = await axios.post(
                            AuthUtils.buildApiUrl(`/playground/v1/agent-template/delete/${agent.agentId}`),
                            {}
                        );

                        if (response.data && response.data.success) {
                            ElMessage.success(`智能体 "${agent.name}" 删除成功！`);
                            // 重新加载智能体列表
                            loadAgentTemplates();
                        } else {
                            throw new Error(response.data?.errMessage || '删除失败');
                        }

                    } catch (error) {
                        if (error === 'cancel') {
                            // 用户取消删除
                            return;
                        }
                        
                        console.error('删除智能体失败:', error);
                        ElMessage.error('删除失败: ' + (error.response?.data?.errMessage || error.message));
                    }
                };

                // 退出登录
                const logout = async () => {
                    try {
                        AuthUtils.logout();
                        ElMessage.success('退出登录成功');
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 1000);
                    } catch (error) {
                        console.error('退出登录失败:', error);
                    }
                };

                // 组件挂载时检查认证状态
                onMounted(() => {
                    if (checkAuth()) {
                        // 默认选择角色扮演菜单
                        selectMenu('roleplay');
                    }
                });

                return {
                    currentUser,
                    currentMenu,
                    agentTemplates,
                    loading,
                    sidebarCollapsed,
                    toggleSidebar,
                    showUserMenu,
                    selectMenu,
                    selectAgent,
                    logout,
                    // 复制相关
                    copyDialogVisible,
                    copyLoading,
                    sourceAgent,
                    copyForm,
                    copyAgent,
                    selectAllCopyOptions,
                    clearAllCopyOptions,
                    selectBasicCopyOptions,
                    handleCloseCopyDialog,
                    confirmCopy,
                    deleteAgent
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>

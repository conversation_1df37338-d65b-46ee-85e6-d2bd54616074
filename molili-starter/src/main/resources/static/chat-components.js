// 聊天组件主入口文件
// 导入所有组件模块

// 加载组件脚本
const loadScript = (src) => {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
};

// 异步加载所有组件
const loadAllComponents = async () => {
    try {
        // 加载各个组件文件
        await loadScript('./components/SystemPromptComponent.js');
        await loadScript('./components/ModelSettingsComponent.js');
        await loadScript('./components/ChatConversationComponent.js');
        
        // 等待所有组件加载完成后，统一导出
        window.ChatComponents = {
            SystemPromptComponent: window.SystemPromptComponent,
            ModelSettingsComponent: window.ModelSettingsComponent,
            ChatConversationComponent: window.ChatConversationComponent
        };
        
        console.log('所有聊天组件加载完成');
        
        // 触发组件加载完成事件
        window.dispatchEvent(new CustomEvent('chat-components-loaded'));
    } catch (error) {
        console.error('加载组件失败:', error);
    }
};

// 页面加载完成后加载组件
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadAllComponents);
} else {
    loadAllComponents();
}

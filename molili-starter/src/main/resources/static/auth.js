/**
 * 认证工具类
 * 提供Cookie操作和用户名相关功能
 */
const AuthUtils = {
    // Cookie名称
    COOKIE_NAME: 'molili_username',
    USER_ID_COOKIE_NAME: 'userId',
    
    // API基础URL
    API_BASE_URL: '/molili',

    /**
     * 获取Cookie的函数
     * @param {string} name Cookie名称
     * @returns {string|null} Cookie值
     */
    getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },

    /**
     * 设置Cookie的函数
     * @param {string} name Cookie名称
     * @param {string} value Cookie值
     * @param {number} days 过期天数，默认30天
     */
    setCookie(name, value, days = 30) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    },

    /**
     * 删除Cookie的函数
     * @param {string} name Cookie名称
     */
    deleteCookie(name) {
        document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    },

    /**
     * 获取当前用户名
     * @returns {string|null} 用户名
     */
    getCurrentUser() {
        return this.getCookie(this.COOKIE_NAME);
    },

    /**
     * 检查用户是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        return !!this.getCurrentUser();
    },

    /**
     * 检查登录状态，未登录时跳转到登录页面
     * @param {string} loginUrl 登录页面URL，默认为login.html
     * @returns {boolean} 是否已登录
     */
    requireAuth(loginUrl = 'login.html') {
        if (!this.isLoggedIn()) {
            window.location.href = loginUrl;
            return false;
        }
        return true;
    },

    /**
     * 登录用户
     * @param {string} username 用户名
     */
    loginUser(username) {
        this.setCookie(this.COOKIE_NAME, username);
    },

    /**
     * 退出登录
     */
    logout() {
        this.deleteCookie(this.COOKIE_NAME);
    },

    /**
     * 构建完整的API URL
     * @param {string} path API路径
     * @returns {string} 完整的API URL
     */
    buildApiUrl(path) {
        // 确保path以/开头
        if (!path.startsWith('/')) {
            path = '/' + path;
        }
        return this.API_BASE_URL + path;
    },

    /**
     * 获取基础URL
     * @returns {string} 基础URL
     */
    getBaseUrl() {
        return this.API_BASE_URL;
    },

    /**
     * 获取用户ID
     * @returns {string|null} 用户ID
     */
    getUserId() {
        return this.getCookie(this.USER_ID_COOKIE_NAME);
    },

    /**
     * 设置用户ID
     * @param {string} userId 用户ID
     */
    setUserId(userId) {
        this.setCookie(this.USER_ID_COOKIE_NAME, userId);
    },

    /**
     * 配置axios拦截器，在请求头中添加用户名
     */
    setupAxiosInterceptor() {
        if (typeof axios !== 'undefined') {
            // 请求拦截器
            axios.interceptors.request.use(
                config => {
                    const username = this.getCurrentUser();
                    if (username) {
                        config.headers['X-Username'] = username;
                        config.headers['X-User-Id'] = username; // 也可以用这个字段
                    }
                    // 设置默认的Content-Type
                    if (!config.headers['Content-Type'] && config.method === 'post') {
                        config.headers['Content-Type'] = 'application/json';
                    }
                    return config;
                },
                error => {
                    return Promise.reject(error);
                }
            );
            
            // 响应拦截器（可选）
            axios.interceptors.response.use(
                response => {
                    return response;
                },
                error => {
                    if (error.response && error.response.status === 401) {
                        // 未授权，清除登录状态并跳转到登录页面
                        this.logout();
                        window.location.href = 'login.html';
                    }
                    return Promise.reject(error);
                }
            );
        }
    }
};

// 如果在浏览器环境中，自动配置axios拦截器
if (typeof window !== 'undefined') {
    // 等待axios加载完成后再配置拦截器
    const checkAxios = () => {
        if (typeof axios !== 'undefined') {
            AuthUtils.setupAxiosInterceptor();
        } else {
            setTimeout(checkAxios, 100);
        }
    };
    checkAxios();
}

// 导出工具类（支持不同的模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthUtils;
} else if (typeof window !== 'undefined') {
    window.AuthUtils = AuthUtils;
} 
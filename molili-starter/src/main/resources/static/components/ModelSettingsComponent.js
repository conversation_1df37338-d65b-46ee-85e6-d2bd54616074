// 流程配置组件
const ModelSettingsComponent = {
    template: `
        <div class="process-config-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"></path>
                    </svg>
                    流程配置
                </h3>
            </div>
            <div class="panel-content">
                <!-- 前思考过程 -->
                <div class="config-section">
                    <div class="section-header">
                        <h4 class="section-title">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                <path d="M12 17h.01"></path>
                            </svg>
                            前思考过程
                        </h4>
                    </div>
                    <div class="section-content">
                        <!-- 用户画像记忆召回 -->
                        <div class="config-item memory-recall-item">
                            <div class="config-item-header">
                                <div class="config-item-title">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                                        <path d="M12 11h4"></path>
                                        <path d="M12 16h4"></path>
                                        <path d="M8 11h.01"></path>
                                        <path d="M8 16h.01"></path>
                                    </svg>
                                    用户画像记忆召回
                                </div>
                                <div class="config-item-actions">
                                    <el-switch
                                        v-model="memoryRecallConfig.enabled"
                                        :disabled="true"
                                        size="small"
                                        style="margin-left: 8px;"
                                    />
                                </div>
                            </div>
                            <div class="config-item-desc">
                                自动召回用户画像信息，为对话提供个性化上下文
                            </div>
                        </div>

                        <!-- 用户情绪识别 -->
                        <div class="config-item">
                            <div class="config-item-header" @click="toggleEmotionCollapse" style="cursor: pointer;">
                                <div class="config-item-title">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                        <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                        <line x1="15" y1="9" x2="15.01" y2="9"></line>
                                    </svg>
                                    用户情绪识别
                                </div>
                                <svg 
                                    width="14" 
                                    height="14" 
                                    viewBox="0 0 24 24" 
                                    fill="none" 
                                    stroke="currentColor" 
                                    stroke-width="2"
                                    :style="{ transform: emotionCollapsed ? 'rotate(0deg)' : 'rotate(180deg)', transition: 'transform 0.3s ease' }"
                                >
                                    <polyline points="6,9 12,15 18,9"></polyline>
                                </svg>
                            </div>
                            <div class="config-item-desc">
                                分析用户输入的情绪状态，识别情感倾向
                            </div>
                            <div v-if="!emotionCollapsed" class="config-item-settings">
                                <div class="setting-group">
                                    <label class="setting-label">模型选择</label>
                                    <el-select v-model="emotionConfig.chatModel" size="small" class="setting-select" placeholder="请选择模型">
                                        <el-option 
                                            v-for="model in models" 
                                            :key="model.value" 
                                            :label="model.name" 
                                            :value="model.value"
                                        />
                                    </el-select>
                                </div>
                                
                                <div class="setting-group">
                                    <label class="setting-label">系统提示词</label>
                                    <el-input
                                        v-model="emotionConfig.systemPrompt"
                                        type="textarea"
                                        :rows="2"
                                        placeholder="点击编辑系统提示词..."
                                        class="setting-textarea"
                                        readonly
                                        @click="openPromptEditor('emotion', 'system')"
                                        style="cursor: pointer;"
                                    />
                                </div>
                                
                                <div class="setting-group">
                                    <label class="setting-label">用户提示词</label>
                                    <el-input
                                        v-model="emotionConfig.userPrompt"
                                        type="textarea"
                                        :rows="2"
                                        placeholder="点击编辑用户提示词..."
                                        class="setting-textarea"
                                        readonly
                                        @click="openPromptEditor('emotion', 'user')"
                                        style="cursor: pointer;"
                                    />
                                </div>
                                
                                <div class="setting-group">
                                    <label class="setting-label">输出变量</label>
                                    <div class="output-variable-group">
                                        <el-input
                                            v-model="emotionConfig.outputVariable"
                                            size="small"
                                            placeholder="变量名"
                                            style="flex: 1; margin-right: 8px;"
                                        />
                                        <el-select v-model="emotionConfig.outputVariableType" size="small" style="width: 120px;">
                                            <el-option label="字符串" :value="1"></el-option>
                                            <el-option label="JSON格式" :value="2"></el-option>
                                            <el-option label="MD格式" :value="3"></el-option>
                                        </el-select>
                                    </div>
                                </div>
                                
                                <div class="setting-actions">
                                    <el-button size="small" type="primary" @click="saveEmotionConfig" :loading="saving">保存</el-button>
                                </div>
                            </div>
                        </div>

                        <!-- Agent情绪模拟 -->
                        <div class="config-item">
                            <div class="config-item-header" @click="toggleSimulationCollapse" style="cursor: pointer;">
                                <div class="config-item-title">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                        <path d="M12 14l3-3 3 3-3 3-3-3"></path>
                                    </svg>
                                    Agent情绪模拟
                                </div>
                                <svg 
                                    width="14" 
                                    height="14" 
                                    viewBox="0 0 24 24" 
                                    fill="none" 
                                    stroke="currentColor" 
                                    stroke-width="2"
                                    :style="{ transform: simulationCollapsed ? 'rotate(0deg)' : 'rotate(180deg)', transition: 'transform 0.3s ease' }"
                                >
                                    <polyline points="6,9 12,15 18,9"></polyline>
                                </svg>
                            </div>
                            <div class="config-item-desc">
                                根据用户情绪调整AI回复的语调和风格
                            </div>
                            <div v-if="!simulationCollapsed" class="config-item-settings">
                                <div class="setting-group">
                                    <label class="setting-label">模型选择</label>
                                    <el-select v-model="simulationConfig.chatModel" size="small" class="setting-select" placeholder="请选择模型">
                                        <el-option 
                                            v-for="model in models" 
                                            :key="model.value" 
                                            :label="model.name" 
                                            :value="model.value"
                                        />
                                    </el-select>
                                </div>
                                
                                <div class="setting-group">
                                    <label class="setting-label">系统提示词</label>
                                    <el-input
                                        v-model="simulationConfig.systemPrompt"
                                        type="textarea"
                                        :rows="2"
                                        placeholder="点击编辑系统提示词..."
                                        class="setting-textarea"
                                        readonly
                                        @click="openPromptEditor('simulation', 'system')"
                                        style="cursor: pointer;"
                                    />
                                </div>
                                
                                <div class="setting-group">
                                    <label class="setting-label">用户提示词</label>
                                    <el-input
                                        v-model="simulationConfig.userPrompt"
                                        type="textarea"
                                        :rows="2"
                                        placeholder="点击编辑用户提示词..."
                                        class="setting-textarea"
                                        readonly
                                        @click="openPromptEditor('simulation', 'user')"
                                        style="cursor: pointer;"
                                    />
                                </div>
                                
                                <div class="setting-group">
                                    <label class="setting-label">输出变量</label>
                                    <div class="output-variable-group">
                                        <el-input
                                            v-model="simulationConfig.outputVariable"
                                            size="small"
                                            placeholder="变量名"
                                            style="flex: 1; margin-right: 8px;"
                                        />
                                        <el-select v-model="simulationConfig.outputVariableType" size="small" style="width: 120px;">
                                            <el-option label="字符串" :value="1"></el-option>
                                            <el-option label="JSON格式" :value="2"></el-option>
                                            <el-option label="MD格式" :value="3"></el-option>
                                        </el-select>
                                    </div>
                                </div>
                                
                                <div class="setting-actions">
                                    <el-button size="small" type="primary" @click="saveSimulationConfig" :loading="saving">保存</el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 后思考过程 -->
                <div class="config-section">
                    <div class="section-header">
                        <h4 class="section-title">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                                <path d="M3 3v5h5"></path>
                                <path d="M12 7v5l4 2"></path>
                            </svg>
                            后思考过程
                        </h4>
                    </div>
                    <div class="section-content">
                        <!-- 用户画像记忆更新 -->
                        <div class="config-item memory-update-item">
                            <div class="config-item-header">
                                <div class="config-item-title">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="7,10 12,15 17,10"></polyline>
                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                    </svg>
                                    用户画像记忆更新
                                </div>
                                <div class="config-item-actions">
                                    <el-switch
                                        v-model="memoryUpdateConfig.enabled"
                                        :disabled="true"
                                        size="small"
                                        style="margin-left: 8px;"
                                    />
                                </div>
                            </div>
                            <div class="config-item-desc">
                                将本次对话信息更新到用户画像记忆库
                            </div>
                        </div>

                        <!-- 可变设定更新 -->
                        <div class="config-item">
                            <div class="config-item-header">
                                <div class="config-item-title">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="3"></circle>
                                        <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"></path>
                                    </svg>
                                    可变设定更新
                                </div>
                            </div>
                            <div class="config-item-desc">
                                配置功能正在开发中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

        </div>

        <!-- 提示词编辑弹窗 -->
        <el-dialog
            v-model="promptEditorVisible"
            :title="promptEditorTitle"
            width="900px"
            :before-close="handleClosePromptEditor"
            :close-on-press-escape="true"
            @keydown.esc="handleClosePromptEditor"
        >
            <div class="prompt-editor-content">
                <div class="prompt-editor-layout">
                    <!-- 左侧编辑区域 -->
                    <div class="prompt-editor-left">
                        <div class="prompt-editor-header">
                            <label class="prompt-editor-label">{{ promptEditorLabel }}</label>
                            <div class="prompt-editor-actions">
                                <el-button size="small" type="text" @click="insertVariableAtCursor">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M12 5v14M5 12h14"></path>
                                    </svg>
                                    插入变量
                                </el-button>
                                <el-button size="small" type="text" @click="clearPromptContent">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="3,6 5,6 21,6"></polyline>
                                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    </svg>
                                    清空
                                </el-button>
                            </div>
                        </div>
                        <el-input
                            ref="promptEditor"
                            v-model="promptEditorContent"
                            type="textarea"
                            :rows="16"
                            placeholder="请输入提示词内容..."
                            class="prompt-editor-textarea"
                            @keydown.esc="handleClosePromptEditor"
                        />
                    </div>
                    
                    <!-- 右侧变量参考区域 -->
                    <div class="prompt-editor-right">
                        <div class="variable-reference-header">
                            <h4 style="margin: 0 0 12px 0; color: #303133; font-size: 14px; font-weight: 500;">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <path d="M12 17h.01"></path>
                                </svg>
                                可引用变量
                            </h4>
                            <p style="margin: 0 0 16px 0; color: #909399; font-size: 12px; line-height: 1.4;">
                                点击变量名可复制到剪贴板，或点击插入按钮插入到光标位置
                            </p>
                        </div>
                        <div class="variable-list">
                            <div 
                                v-for="variable in variableList" 
                                :key="variable.name"
                                class="variable-item"
                            >
                                <div class="variable-info">
                                    <div class="variable-name" @click="copyVariable(variable.name)">{{ variable.name }}</div>
                                    <div class="variable-desc">{{ variable.description }}</div>
                                </div>
                                <div class="variable-actions">
                                    <el-button size="small" type="text" @click="insertVariable(variable.name)" title="插入到光标位置">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M12 5v14M5 12h14"></path>
                                        </svg>
                                    </el-button>
                                    <el-button size="small" type="text" @click="copyVariable(variable.name)" title="复制到剪贴板">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                        </svg>
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleClosePromptEditor">取消</el-button>
                    <el-button type="primary" @click="savePromptContent">保存</el-button>
                </span>
            </template>
        </el-dialog>
    `,
    props: {
        agentId: String
    },
    data() {
        return {
            // 折叠状态
            emotionCollapsed: true,
            simulationCollapsed: true,
            
            // 模型列表
            models: [],
            
            // 用户画像记忆召回配置
            memoryRecallConfig: {
                enabled: true
            },
            
            // 用户画像记忆更新配置
            memoryUpdateConfig: {
                enabled: true
            },
            
            // 用户情绪识别配置
            emotionConfig: {
                agentId: 'recognizeEmotion',
                systemPrompt: '',
                userPrompt: '',
                chatModel: '',
                outputVariable: '',
                outputVariableType: 1
            },
            
            // 情绪模拟配置
            simulationConfig: {
                agentId: 'generateAgentEmotion',
                systemPrompt: '',
                userPrompt: '',
                chatModel: '',
                outputVariable: '',
                outputVariableType: 1
            },
            

            
            // 保存状态
            saving: false,
            loading: false,
            
            // 提示词编辑弹窗
            promptEditorVisible: false,
            promptEditorTitle: '',
            promptEditorLabel: '',
            promptEditorContent: '',
            currentEditingType: '', // 'emotion' or 'simulation'
            currentEditingField: '', // 'system' or 'user'
            variableList: [
                { name: '{user_id}', description: '用户ID' },
                { name: '{agent_name}', description: '智能体名称' },
                { name: '{user_input}', description: '用户本次提问' },
                { name: '{conversation_id}', description: '会话ID' },
                { name: '{natural_attributes}', description: '自然属性设定' },
                { name: '{temperament_profile}', description: '气质人格设定' },
                { name: '{interaction_profile}', description: '交互设定' },
                { name: '{trait_profile}', description: '特质人格设定' },
                { name: '{chat_memory}', description: '最近N轮对话' },
                { name: '{agent_last_emotion}', description: '智能体上一轮情绪向量' },
                { name: '{user_emotion}', description: '本轮用户情绪向量（注：组件执行后才会有）' },
                { name: '{agent_emotion}', description: '本轮智能体情绪向量（注：组件执行后才会有）' },
                { name: '{user_profile}', description: '记忆库-用户画像' }
            ]
        };
    },
    mounted() {
        this.loadModels();
        this.loadEmotionConfig();
        this.loadSimulationConfig();
        
        // 监听ESC键关闭弹窗
        document.addEventListener('keydown', this.handleEscKey);
    },
    
    beforeUnmount() {
        // 清理事件监听器
        document.removeEventListener('keydown', this.handleEscKey);
    },
    methods: {
        // 切换折叠状态
        toggleEmotionCollapse() {
            this.emotionCollapsed = !this.emotionCollapsed;
        },
        
        toggleSimulationCollapse() {
            this.simulationCollapsed = !this.simulationCollapsed;
        },
        

        
        // 加载模型列表
        async loadModels() {
            try {
                const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/model/list'));
                if (response.data && response.data.success) {
                    this.models = response.data.data || [];
                }
            } catch (error) {
                console.error('加载模型列表失败:', error);
                const { ElMessage } = ElementPlus;
                ElMessage.error('加载模型列表失败');
            }
        },
        
        // 加载用户情绪识别配置
        async loadEmotionConfig() {
            this.loading = true;
            try {
                const response = await axios.post(AuthUtils.buildApiUrl(`/playground/v1/tool-agent/info/recognizeEmotion`));
                if (response.data && response.data.success && response.data.data) {
                    const data = response.data.data;
                    this.emotionConfig = {
                        agentId: data.agentId || 'recognizeEmotion',
                        systemPrompt: data.systemPrompt || '',
                        userPrompt: data.userPrompt || '',
                        chatModel: data.chatModel || '',
                        outputVariable: data.outputVariable || '',
                        outputVariableType: data.outputVariableType || 1
                    };
                } else if (response.data && !response.data.success && response.data.errCode === '404') {
                    // 工具Agent不存在，使用默认配置
                    console.info('工具Agent不存在，使用默认配置');
                    this.emotionConfig = {
                        agentId: 'recognizeEmotion',
                        systemPrompt: '',
                        userPrompt: '',
                        chatModel: '',
                        outputVariable: '',
                        outputVariableType: 1
                    };
                }
            } catch (error) {
                if (error.response && error.response.status === 404) {
                    // 404错误，工具Agent不存在，使用默认配置
                    console.info('工具Agent不存在，使用默认配置');
                    this.emotionConfig = {
                        agentId: 'recognizeEmotion',
                        systemPrompt: '',
                        userPrompt: '',
                        chatModel: '',
                        outputVariable: '',
                        outputVariableType: 1
                    };
                } else {
                    console.error('加载用户情绪识别配置失败:', error);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('加载配置失败');
                }
            } finally {
                this.loading = false;
            }
        },
        
        // 加载情绪模拟配置
        async loadSimulationConfig() {
            this.loading = true;
            try {
                const response = await axios.post(AuthUtils.buildApiUrl(`/playground/v1/tool-agent/info/generateAgentEmotion`));
                if (response.data && response.data.success && response.data.data) {
                    const data = response.data.data;
                    this.simulationConfig = {
                        agentId: data.agentId || 'generateAgentEmotion',
                        systemPrompt: data.systemPrompt || '',
                        userPrompt: data.userPrompt || '',
                        chatModel: data.chatModel || '',
                        outputVariable: data.outputVariable || '',
                        outputVariableType: data.outputVariableType || 1
                    };
                } else if (response.data && !response.data.success && response.data.errCode === '404') {
                    // 工具Agent不存在，使用默认配置
                    console.info('情绪模拟Agent不存在，使用默认配置');
                    this.simulationConfig = {
                        agentId: 'generateAgentEmotion',
                        systemPrompt: '',
                        userPrompt: '',
                        chatModel: '',
                        outputVariable: '',
                        outputVariableType: 1
                    };
                }
            } catch (error) {
                if (error.response && error.response.status === 404) {
                    // 404错误，工具Agent不存在，使用默认配置
                    console.info('情绪模拟Agent不存在，使用默认配置');
                    this.simulationConfig = {
                        agentId: 'generateAgentEmotion',
                        systemPrompt: '',
                        userPrompt: '',
                        chatModel: '',
                        outputVariable: '',
                        outputVariableType: 1
                    };
                } else {
                    console.error('加载情绪模拟配置失败:', error);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('加载配置失败');
                }
            } finally {
                this.loading = false;
            }
        },
        
        // 保存用户情绪识别配置
        async saveEmotionConfig() {
            if (!this.emotionConfig.chatModel) {
                const { ElMessage } = ElementPlus;
                ElMessage.warning('请选择模型');
                return;
            }
            
            this.saving = true;
            try {
                const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/tool-agent/update'), {
                    agentId: 'recognizeEmotion',
                    systemPrompt: this.emotionConfig.systemPrompt,
                    userPrompt: this.emotionConfig.userPrompt,
                    chatModel: this.emotionConfig.chatModel,
                    outputVariable: this.emotionConfig.outputVariable,
                    outputVariableType: this.emotionConfig.outputVariableType
                });
                
                if (response.data && response.data.success) {
                    const { ElMessage } = ElementPlus;
                    ElMessage.success('配置保存成功');
                    this.$emit('config-change', this.emotionConfig);
                } else if (response.data && !response.data.success && response.data.errCode === '404') {
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('工具Agent不存在，请先在数据库中创建记录');
                } else {
                    throw new Error(response.data?.errMessage || '保存失败');
                }
            } catch (error) {
                console.error('保存用户情绪识别配置失败:', error);
                const { ElMessage } = ElementPlus;
                
                if (error.response && error.response.status === 404) {
                    ElMessage.error('工具Agent不存在，请先在数据库中创建记录');
                } else {
                    ElMessage.error('保存失败: ' + (error.response?.data?.errMessage || error.message));
                }
            } finally {
                this.saving = false;
            }
        },
        
        // 保存情绪模拟配置
        async saveSimulationConfig() {
            if (!this.simulationConfig.chatModel) {
                const { ElMessage } = ElementPlus;
                ElMessage.warning('请选择模型');
                return;
            }
            
            this.saving = true;
            try {
                const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/tool-agent/update'), {
                    agentId: 'generateAgentEmotion',
                    systemPrompt: this.simulationConfig.systemPrompt,
                    userPrompt: this.simulationConfig.userPrompt,
                    chatModel: this.simulationConfig.chatModel,
                    outputVariable: this.simulationConfig.outputVariable,
                    outputVariableType: this.simulationConfig.outputVariableType
                });
                
                if (response.data && response.data.success) {
                    const { ElMessage } = ElementPlus;
                    ElMessage.success('配置保存成功');
                    this.$emit('config-change', this.simulationConfig);
                } else if (response.data && !response.data.success && response.data.errCode === '404') {
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('工具Agent不存在，请先在数据库中创建记录');
                } else {
                    throw new Error(response.data?.errMessage || '保存失败');
                }
            } catch (error) {
                console.error('保存情绪模拟配置失败:', error);
                const { ElMessage } = ElementPlus;
                
                if (error.response && error.response.status === 404) {
                    ElMessage.error('工具Agent不存在，请先在数据库中创建记录');
                } else {
                    ElMessage.error('保存失败: ' + (error.response?.data?.errMessage || error.message));
                }
            } finally {
                this.saving = false;
            }
        },
        
        // 打开提示词编辑器
        openPromptEditor(type, field) {
            this.currentEditingType = type;
            this.currentEditingField = field;
            
            // 设置弹窗标题和标签
            const typeMap = {
                'emotion': '用户情绪识别',
                'simulation': 'Agent情绪模拟'
            };
            const fieldMap = {
                'system': '系统提示词',
                'user': '用户提示词'
            };
            
            this.promptEditorTitle = `编辑${typeMap[type]} - ${fieldMap[field]}`;
            this.promptEditorLabel = fieldMap[field];
            
            // 设置当前内容
            if (type === 'emotion') {
                this.promptEditorContent = field === 'system' ? this.emotionConfig.systemPrompt : this.emotionConfig.userPrompt;
            } else {
                this.promptEditorContent = field === 'system' ? this.simulationConfig.systemPrompt : this.simulationConfig.userPrompt;
            }
            
            this.promptEditorVisible = true;
            
            // 聚焦到编辑器
            this.$nextTick(() => {
                if (this.$refs.promptEditor && this.$refs.promptEditor.focus) {
                    this.$refs.promptEditor.focus();
                }
            });
        },
        
        // 关闭提示词编辑器
        handleClosePromptEditor() {
            this.promptEditorVisible = false;
            this.promptEditorContent = '';
            this.currentEditingType = '';
            this.currentEditingField = '';
        },
        
        // 保存提示词内容
        savePromptContent() {
            if (this.currentEditingType === 'emotion') {
                if (this.currentEditingField === 'system') {
                    this.emotionConfig.systemPrompt = this.promptEditorContent;
                } else {
                    this.emotionConfig.userPrompt = this.promptEditorContent;
                }
            } else {
                if (this.currentEditingField === 'system') {
                    this.simulationConfig.systemPrompt = this.promptEditorContent;
                } else {
                    this.simulationConfig.userPrompt = this.promptEditorContent;
                }
            }
            this.handleClosePromptEditor();
        },
        
        // 插入变量到光标位置
        insertVariable(variableName) {
            const textarea = this.$refs.promptEditor?.$el?.querySelector('textarea');
            if (textarea) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const content = this.promptEditorContent;
                
                this.promptEditorContent = content.substring(0, start) + variableName + content.substring(end);
                
                // 重新设置光标位置
                this.$nextTick(() => {
                    textarea.focus();
                    textarea.setSelectionRange(start + variableName.length, start + variableName.length);
                });
            }
        },
        
        // 插入变量到光标位置（按钮触发）
        insertVariableAtCursor() {
            const { ElMessage } = ElementPlus;
            ElMessage.info('请点击右侧变量列表中的插入按钮来插入变量');
        },
        
        // 清空提示词内容
        clearPromptContent() {
            this.promptEditorContent = '';
            this.$nextTick(() => {
                if (this.$refs.promptEditor && this.$refs.promptEditor.focus) {
                    this.$refs.promptEditor.focus();
                }
            });
        },
        
        // 复制变量到剪贴板
        async copyVariable(variableName) {
            try {
                await navigator.clipboard.writeText(variableName);
                const { ElMessage } = ElementPlus;
                ElMessage.success(`已复制变量 ${variableName} 到剪贴板`);
            } catch (error) {
                console.error('复制失败:', error);
                // 降级处理：使用传统的复制方法
                const textArea = document.createElement('textarea');
                textArea.value = variableName;
                textArea.style.position = 'absolute';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    const { ElMessage } = ElementPlus;
                    ElMessage.success(`已复制变量 ${variableName} 到剪贴板`);
                } catch (fallbackError) {
                    console.error('降级复制也失败:', fallbackError);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('复制失败，请手动复制');
                } finally {
                    document.body.removeChild(textArea);
                }
            }
        },
        
        // 处理ESC键事件
        handleEscKey(event) {
            if (event.key === 'Escape' && this.promptEditorVisible) {
                this.handleClosePromptEditor();
            }
        }
    }
};

// 导出组件
window.ModelSettingsComponent = ModelSettingsComponent; 
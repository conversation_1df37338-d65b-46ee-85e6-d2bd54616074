// 聊天对话组件
const ChatConversationComponent = {
    template: `
        <div class="chat-conversation-panel">
            <div class="panel-header">
                <div class="header-left">
                    <div class="agent-info">
                        <div class="agent-avatar">
                            {{ currentAgent.name ? currentAgent.name.charAt(0) : 'A' }}
                        </div>
                        <div class="agent-details">
                            <div class="agent-name">{{ currentAgent.name || '智能助手' }}</div>
                            <div class="agent-status">
                                <div class="status-dot"></div>
                                <span>在线</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="action-btn" @click="clearChatMemory" title="清除上下文">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                    </button>
                    <button class="action-btn" @click="queryUserProfile" title="长期记忆-用户画像">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                            <path d="M12 14l9-5-9-5-9 5 9 5"></path>
                        </svg>
                    </button>
                    <button class="action-btn" @click="exportChat" title="导出聊天记录">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15V19A2 2 0 0 1 19 21H5A2 2 0 0 1 3 19V15"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="messages-container" ref="messagesContainer" @scroll="handleScroll">
                <!-- 加载更多指示器 -->
                <div v-if="isLoadingMore" class="loading-more-indicator">
                    <div class="loading-spinner"></div>
                    <span>加载更多消息...</span>
                </div>

                <div v-if="messages.length === 0 && !isLoadingHistory" class="welcome-message">
                    <div class="welcome-icon">🤖</div>
                    <h3 class="welcome-title">开始与 {{ currentAgent.name }} 对话</h3>
                    <p class="welcome-description">我是您的AI助手，很高兴为您服务！请输入您的问题或需求，我会尽力帮助您。</p>
                    <div class="welcome-suggestions">
                        <div class="suggestion-card" :class="{ 'disabled': isReceiving }" @click="quickMessage('你好，请介绍一下自己')">
                            <div class="suggestion-icon">👋</div>
                            <div class="suggestion-text">打招呼</div>
                        </div>
                        <div class="suggestion-card" :class="{ 'disabled': isReceiving }" @click="quickMessage('你都能做什么？')">
                            <div class="suggestion-icon">🛠️</div>
                            <div class="suggestion-text">功能介绍</div>
                        </div>
                        <div class="suggestion-card" :class="{ 'disabled': isReceiving }" @click="quickMessage('帮我分析一下这个问题')">
                            <div class="suggestion-icon">🔍</div>
                            <div class="suggestion-text">问题分析</div>
                        </div>
                    </div>
                </div>

                <!-- 历史消息加载中 -->
                <div v-if="isLoadingHistory" class="loading-history-indicator">
                    <div class="loading-spinner"></div>
                    <span>加载历史消息...</span>
                </div>

                <div v-for="message in messages" :key="message.id" class="message" :class="message.role">
                    <div class="message-avatar" v-if="message.role === 'user'">
                        {{ getUserInitial() }}
                    </div>
                    <div class="message-avatar" v-else>
                        {{ currentAgent.name ? currentAgent.name.charAt(0) : 'A' }}
                    </div>
                    <div class="message-body">
                        <!-- 洞察信息运行过程 -->
                        <div v-if="getMessageInsights(message).length > 0" class="insight-process-container">
                            <div class="insight-process-header" @click="toggleInsightProcess(message.id)">
                                <div class="insight-process-icon">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 12l2 2 4-4"></path>
                                        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                                        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                                    </svg>
                                </div>
                                <span class="insight-process-title">隐蔽运行过程</span>
                                <div class="insight-process-info">
                                    <span class="insight-process-stats">
                                        {{ getInsightStats(message) }}
                                    </span>
                                    <div class="insight-process-toggle" :class="{ 'expanded': isInsightProcessExpanded(message.id) }">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="6,9 12,15 18,9"></polyline>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div v-if="isInsightProcessExpanded(message.id)" class="insight-process-content">
                                <div class="insight-process-summary">
                                    <div class="process-status completed">
                                        <div class="status-icon">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M9 12l2 2 4-4"></path>
                                                <circle cx="12" cy="12" r="10"></circle>
                                            </svg>
                                        </div>
                                        <span>运行完成</span>
                                        <span class="process-time">{{ getProcessTime(message) }}</span>
                                    </div>
                                </div>

                                <div class="insight-items">
                                    <div v-for="insight in getMessageInsights(message)" :key="insight.id"
                                         class="insight-item"
                                         :class="{ 'expanded': isInsightExpanded(insight.id) }">
                                        <div class="insight-item-header" @click="toggleInsight(insight.id)">
                                            <div class="insight-item-left">
                                                <div class="insight-item-icon">
                                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M9 12l2 2 4-4"></path>
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                    </svg>
                                                </div>
                                                <span class="insight-item-title">{{ insight.title || '洞察信息' }}</span>
                                            </div>
                                            <div class="insight-item-right">
                                                <span class="insight-item-time">{{ insight.duration || '1.2s' }}</span>
                                                <div class="insight-item-toggle" :class="{ 'expanded': isInsightExpanded(insight.id) }">
                                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <polyline points="6,9 12,15 18,9"></polyline>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="isInsightExpanded(insight.id)" class="insight-item-content">
                                            <div class="insight-content-wrapper">
                                                <div class="insight-content-text">{{ insight.content }}</div>
                                                <div v-if="insight.metadata" class="insight-metadata">
                                                    <div v-for="(value, key) in insight.metadata" :key="key" class="metadata-item">
                                                        <span class="metadata-key">{{ key }}:</span>
                                                        <span class="metadata-value">{{ value }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="message-content" :class="{ 'streaming': message.isStreaming }">
                            <span class="message-text">{{ message.content }}</span>
                            <div v-if="message.isStreaming" class="loading-spinner"></div>
                        </div>
                        <!-- 显示消息中的图片 -->
                        <div v-if="message.images && message.images.length > 0" class="message-images">
                            <div v-for="(image, index) in message.images" :key="index" class="message-image-container">
                                <img :src="image.url" :alt="image.name" class="message-image" @click="previewImage(image.url)">
                            </div>
                        </div>
                        <div class="message-time" :title="formatFullTime(message.timestamp)">{{ formatTime(message.timestamp) }}</div>
                    </div>
                </div>
            </div>

            <!-- 图片预览区域 -->
            <div v-if="uploadedImages.length > 0" class="image-preview-area">
                <div v-for="(image, index) in uploadedImages" :key="index" class="image-preview-item">
                    <img :src="image.url" :alt="image.name" class="preview-image">
                    <button class="remove-image-btn" @click="removeImage(index)" title="删除图片">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="input-area">
                <div class="input-wrapper">
                    <button class="action-btn clear-chat-btn" @click="clearChat" title="清空聊天">
                        <svg width="20" height="20" viewBox="0 0 1024 1024" fill="currentColor">
                            <path d="M975.36 489.984c0.512-53.248-42.496-96.768-95.744-97.28h-227.84V200.192c0.512-65.536-52.736-119.296-118.272-119.808H501.76c-65.536 0.512-118.784 54.272-118.272 119.808v192.512H151.04c-53.76 0.512-96.256 44.544-95.744 98.304 0.512 41.984 28.16 78.848 68.096 91.648v339.456c-0.512 43.008 34.304 77.824 77.312 78.336h635.392c43.008-0.512 77.312-35.328 77.312-78.336v-341.504c37.376-14.336 61.952-50.688 61.952-90.624z m-532.48-289.792c0-32.768 26.112-59.392 58.88-59.392h31.744c32.768 0.512 58.88 27.136 58.88 59.392v192.512H442.88V200.192z m393.216 739.84h-97.792v-123.392c0.512-16.384-12.288-30.208-28.672-30.72-16.384-0.512-30.208 12.288-30.72 28.672v125.44h-130.56v-123.392c0-16.384-13.312-29.696-29.696-29.696-16.384 0-29.696 13.312-29.696 29.696v123.392H358.4v-123.392c0-16.384-13.312-29.696-29.696-29.696-16.384 0-29.696 13.312-29.696 29.696v123.392H200.704c-9.728 0-17.92-8.192-17.92-17.92v-334.848h671.232v334.848c0 9.728-8.192 17.92-17.92 17.92z m43.52-413.184H151.04c-20.48 0-36.864-16.896-36.864-37.376s16.384-36.864 36.864-36.864h728.576c20.48 0 36.864 16.896 36.864 37.376s-16.384 36.864-36.864 36.864z"></path>
                        </svg>
                    </button>
                    <textarea
                        v-model="inputMessage"
                        class="input-field"
                        :placeholder="isReceiving ? '智能体正在回答中...' : '输入您的消息...'"
                        @keydown.enter="handleEnter"
                        @compositionstart="onCompositionStart"
                        @compositionend="onCompositionEnd"
                        @input="autoResize"
                        ref="inputField"
                        rows="1"
                        :disabled="isReceiving"
                    ></textarea>
                    <!-- 图片上传按钮 -->
                    <button 
                        class="action-btn upload-image-btn" 
                        @click="triggerImageUpload" 
                        title="上传图片"
                        :disabled="isReceiving"
                    >
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                    </button>
                    <input 
                        type="file" 
                        ref="imageInput" 
                        @change="handleImageUpload" 
                        accept="image/*" 
                        style="display: none;"
                    >
                    <button
                        class="send-button"
                        :class="{ 'stop-button': isReceiving }"
                        @click="sendMessage"
                        :disabled="!isReceiving && !inputMessage.trim() && uploadedImages.length === 0"
                        :title="isReceiving ? '停止回答' : '发送'"
                    >
                        <svg v-if="!isReceiving" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 2L11 13"></path>
                            <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                        </svg>
                        <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                            <rect x="6" y="6" width="12" height="12" rx="2"></rect>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 用户画像弹窗 -->
            <el-dialog
                v-model="userProfileDialogVisible"
                title="长期记忆 - 用户画像"
                width="600px"
                :before-close="handleUserProfileDialogClose"
                class="user-profile-dialog"
            >
                <div class="user-profile-content">
                    <div v-if="userProfileLoading" class="loading-container">
                        <div class="loading-spinner"></div>
                        <span>加载用户画像中...</span>
                    </div>
                    <div v-else-if="userProfileData" class="profile-json-container">
                        <div class="profile-header">
                            <div class="profile-title">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                用户画像数据
                            </div>
                            <div class="profile-actions">
                                <button class="copy-btn" @click="copyProfileData" title="复制数据">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="profile-json-wrapper">
                            <pre class="profile-json-content">{{ formatJsonData(userProfileData) }}</pre>
                        </div>
                    </div>
                    <div v-else class="profile-error">
                        <div class="error-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>
                        </div>
                        <div class="error-text">无法加载用户画像数据</div>
                    </div>
                </div>
            </el-dialog>
        </div>
    `,
    props: {
        currentAgent: Object,
        agentId: String
    },
    data() {
        return {
            messages: [],
            inputMessage: '',
            isReceiving: false,
            currentReader: null,
            expandedInsights: new Set(),
            expandedInsightProcesses: new Set(),
            isLoadingHistory: false,
            isLoadingMore: false,
            hasMoreHistory: true,
            lastMessageId: null,
            // 用户画像相关状态
            userProfileDialogVisible: false,
            userProfileLoading: false,
            userProfileData: null,
            userProfileError: null,
            // 输入法状态
            isComposing: false,
            // 图片上传相关状态
            uploadedImages: []
        };
    },
    mounted() {
        // 组件挂载时加载历史消息
        this.loadHistoryMessages();
        
        // 添加全局ESC键监听
        document.addEventListener('keydown', this.handleGlobalKeydown);
    },
    beforeUnmount() {
        // 移除全局ESC键监听
        document.removeEventListener('keydown', this.handleGlobalKeydown);
    },
    watch: {
        // 监听agentId变化，重新加载历史消息
        agentId(newAgentId, oldAgentId) {
            if (newAgentId && newAgentId !== oldAgentId) {
                this.resetAndLoadHistory();
            }
        }
    },
    methods: {
        // 重置状态并重新加载历史消息
        resetAndLoadHistory() {
            // 重置消息列表和状态
            this.messages = [];
            this.expandedInsights.clear();
            this.expandedInsightProcesses.clear();
            this.hasMoreHistory = true;
            this.lastMessageId = null;
            this.isLoadingHistory = false;
            this.isLoadingMore = false;
            
            // 重新加载历史消息
            this.loadHistoryMessages();
        },

        // 加载历史消息
        async loadHistoryMessages(beforeId = '0') {
            if (!this.agentId) return;
            
            const isInitialLoad = beforeId === '0';
            
            if (isInitialLoad) {
                this.isLoadingHistory = true;
            } else {
                this.isLoadingMore = true;
            }

            try {
                const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/chat-message/fetch'), {
                    agentId: this.agentId,
                    beforeId: beforeId,
                    limit: 20
                });

                if (response.data && response.data.success) {
                    const historyMessages = response.data.data || [];
                    
                    if (historyMessages.length === 0) {
                        this.hasMoreHistory = false;
                        return;
                    }

                    // 转换历史消息格式
                    const formattedMessages = this.formatHistoryMessages(historyMessages);
                    
                    if (isInitialLoad) {
                        // 初次加载，直接设置消息
                        this.messages = formattedMessages;
                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                    } else {
                        // 加载更多，添加到开头
                        const container = this.$refs.messagesContainer;
                        const oldScrollHeight = container.scrollHeight;
                        
                        this.messages.unshift(...formattedMessages);
                        
                        this.$nextTick(() => {
                            // 保持滚动位置
                            const newScrollHeight = container.scrollHeight;
                            container.scrollTop = newScrollHeight - oldScrollHeight;
                        });
                    }

                    // 记录最后一条消息ID用于下次加载
                    if (formattedMessages.length > 0) {
                        this.lastMessageId = formattedMessages[0].msgId;
                    }

                    // 检查是否还有更多历史消息
                    if (historyMessages.length < 20) {
                        this.hasMoreHistory = false;
                    }
                } else {
                    console.error('加载历史消息失败:', response.data?.errMessage);
                }
            } catch (error) {
                console.error('加载历史消息失败:', error);
            } finally {
                this.isLoadingHistory = false;
                this.isLoadingMore = false;
            }
        },

        // 格式化历史消息
        formatHistoryMessages(historyMessages) {
            const formattedMessages = [];
            
            // 按时间正序排列（最老的在前面）
            const sortedMessages = [...historyMessages].sort((a, b) => 
                new Date(a.createTime) - new Date(b.createTime)
            );
            
            for (const msg of sortedMessages) {
                // 规范化角色值，确保与CSS样式匹配
                let normalizedRole = msg.role;
                if (msg.role && typeof msg.role === 'string') {
                    const lowerRole = msg.role.toLowerCase();
                    if (lowerRole === 'user' || lowerRole === 'human') {
                        normalizedRole = 'user';
                    } else if (lowerRole === 'assistant' || lowerRole === 'ai' || lowerRole === 'bot') {
                        normalizedRole = 'assistant';
                    }
                }
                
                // 直接根据后端返回的ChatMessageCo结构处理
                const formattedMessage = {
                    id: msg.msgId,
                    msgId: msg.msgId,
                    role: normalizedRole,
                    content: msg.content || '',
                    timestamp: new Date(msg.createTime),
                    isStreaming: false
                };
                
                // 添加洞察信息 - 只有assistant角色的消息才可能有洞察信息
                if (normalizedRole === 'assistant' && msg.ext && msg.ext.progressList && msg.ext.progressList.length > 0) {
                    formattedMessage.insights = this.formatInsights(msg.ext.progressList);
                }
                
                formattedMessages.push(formattedMessage);
            }
            
            return formattedMessages;
        },

        // 格式化洞察信息
        formatInsights(progressList) {
            const insights = [];
            
            for (const progressStr of progressList) {
                try {
                    const progress = JSON.parse(progressStr);
                    insights.push({
                        id: `insight-${Date.now()}-${Math.random()}`,
                        type: 'progress',
                        title: progress.title || '处理步骤',
                        content: progress.content || '',
                        duration: '1.0s',
                        metadata: progress.metadata || {}
                    });
                } catch (e) {
                    console.error('解析洞察信息失败:', e);
                }
            }
            
            return insights;
        },

        // 处理滚动事件，实现触顶加载
        handleScroll() {
            const container = this.$refs.messagesContainer;
            if (!container) return;

            // 检查是否滚动到顶部
            if (container.scrollTop <= 10 && this.hasMoreHistory && !this.isLoadingMore && !this.isLoadingHistory) {
                this.loadHistoryMessages(this.lastMessageId || '0');
            }
        },

        getUserInitial() {
            return 'U';
        },
        quickMessage(message) {
            if (this.isReceiving) {
                const { ElMessage } = ElementPlus;
                ElMessage.warning('请等待当前回复完成后再发送新消息');
                return;
            }
            this.inputMessage = message;
            this.$nextTick(() => {
                this.$refs.inputField.focus();
                this.autoResize();
            });
        },
        // 输入法开始输入
        onCompositionStart() {
            this.isComposing = true;
        },
        // 输入法结束输入
        onCompositionEnd() {
            this.isComposing = false;
        },
        handleEnter(event) {
            // 如果正在输入法输入状态，不处理回车事件
            if (this.isComposing || event.isComposing) {
                return;
            }
            
            if (event.shiftKey) {
                return;
            }
            
            // 阻止默认行为（换行）
            event.preventDefault();
            this.sendMessage();
        },
        autoResize() {
            const textarea = this.$refs.inputField;
            if (textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }
        },
        sendMessage() {
            if (this.isReceiving) {
                this.stopStreaming();
                return;
            }

            const content = this.inputMessage.trim();
            if (!content && this.uploadedImages.length === 0) return;

            const userMessage = {
                id: Date.now(),
                role: 'user',
                content: content,
                timestamp: new Date()
            };

            if (this.uploadedImages.length > 0) {
                userMessage.images = this.uploadedImages.map(img => ({
                    url: img.url,
                    name: img.name
                }));
                // 清空上传的图片，但在发送消息后
            }

            this.messages.push(userMessage);

            this.inputMessage = '';
            this.autoResize();
            this.scrollToBottom();

            this.$emit('message-sent', userMessage);
            
            // 清空上传的图片
            this.uploadedImages = [];
        },
        stopStreaming() {
            if (this.currentReader) {
                this.currentReader.cancel();
                this.currentReader = null;
            }
            this.isReceiving = false;

            const lastMessage = this.messages[this.messages.length - 1];
            if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
                lastMessage.isStreaming = false;
                lastMessage.content += '\n\n[回答被中断]';
            }

            const { ElMessage } = ElementPlus;
            ElMessage.info('已停止回答');
        },
        clearChat() {
            const { ElMessageBox, ElMessage } = ElementPlus;
            ElMessageBox.confirm(
                '确定要清空所有聊天记录吗？\n\n注意：该操作不会清除对话记忆，AI依然会记住你们之前的对话内容。', 
                '清空聊天', 
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            ).then(async () => {
                try {
                    // 调用后端接口清空聊天记录
                    const response = await axios.post(AuthUtils.buildApiUrl(`/playground/v1/chat-message/clear/${this.agentId}`));
                    
                    if (response.data && response.data.success) {
                        // 清空成功，更新前端状态
                        this.messages = [];
                        this.expandedInsights.clear();
                        this.expandedInsightProcesses.clear();
                        this.hasMoreHistory = true;
                        this.lastMessageId = null;
                        ElMessage.success('聊天记录已清空');
                    } else {
                        throw new Error(response.data?.errMessage || '清空聊天记录失败');
                    }
                } catch (error) {
                    console.error('清空聊天记录失败:', error);
                    ElMessage.error('清空聊天记录失败: ' + (error.response?.data?.errMessage || error.message));
                }
            }).catch(() => {});
        },
        clearChatMemory() {
            const { ElMessageBox, ElMessage } = ElementPlus;
            ElMessageBox.confirm(
                '确定要清除当前对话的上下文记忆吗？\n\n清除后，AI将不再记住本次对话的历史内容。', 
                '清除上下文', 
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            ).then(async () => {
                try {
                    // 调用后端接口清除上下文记忆
                    const response = await axios.post(AuthUtils.buildApiUrl(`/playground/v1/chat-message/chat-memory/clear/${this.agentId}`));
                    
                    if (response.data && response.data.success) {
                        ElMessage.success('上下文记忆已清除');
                    } else {
                        throw new Error(response.data?.errMessage || '清除上下文记忆失败');
                    }
                } catch (error) {
                    console.error('清除上下文记忆失败:', error);
                    ElMessage.error('清除上下文记忆失败: ' + (error.response?.data?.errMessage || error.message));
                }
            }).catch(() => {});
        },
        exportChat() {
            const chatData = this.messages.map(msg => ({
                role: msg.role,
                content: msg.content,
                time: this.formatTime(msg.timestamp)
            }));

            const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'chat-' + new Date().toISOString().split('T')[0] + '.json';
            a.click();
            URL.revokeObjectURL(url);

            const { ElMessage } = ElementPlus;
            ElMessage.success('聊天记录已导出');
        },
        formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            
            // 如果是今天的消息，只显示时间
            if (messageDate.getTime() === today.getTime()) {
                return date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // 如果是昨天的消息
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            if (messageDate.getTime() === yesterday.getTime()) {
                return '昨天 ' + date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // 如果是本年的消息，显示月-日 时:分
            if (date.getFullYear() === now.getFullYear()) {
                return date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                }) + ' ' + date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // 更早的消息显示完整日期时间
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }) + ' ' + date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },
        formatFullTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                weekday: 'short'
            });
        },
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },
        getMessageInsights(message) {
            return message.insights || [];
        },
        toggleInsight(insightId) {
            const expanded = this.expandedInsights;
            if (expanded.has(insightId)) {
                expanded.delete(insightId);
            } else {
                expanded.add(insightId);
            }
        },
        isInsightExpanded(insightId) {
            return this.expandedInsights.has(insightId);
        },
        toggleInsightProcess(messageId) {
            const expanded = this.expandedInsightProcesses;
            if (expanded.has(messageId)) {
                expanded.delete(messageId);
            } else {
                expanded.add(messageId);
            }
        },
        isInsightProcessExpanded(messageId) {
            return this.expandedInsightProcesses.has(messageId);
        },
        getInsightStats(message) {
            const insights = this.getMessageInsights(message);
            if (insights.length === 0) return '';

            const totalTime = insights.reduce((sum, insight) => {
                const duration = insight.duration || '1.2s';
                const time = parseFloat(duration.replace('s', ''));
                return sum + time;
            }, 0);

            return `${insights.length}个步骤 · ${totalTime.toFixed(1)}s`;
        },
        getProcessTime(message) {
            const insights = this.getMessageInsights(message);
            if (insights.length === 0) return '0.0s';

            const totalTime = insights.reduce((sum, insight) => {
                const duration = insight.duration || '1.2s';
                const time = parseFloat(duration.replace('s', ''));
                return sum + time;
            }, 0);

            return `${totalTime.toFixed(1)}s`;
        },

        // 查询用户画像
        async queryUserProfile() {
            this.userProfileDialogVisible = true;
            await this.loadUserProfile();
        },

        // 加载用户画像数据
        async loadUserProfile() {
            this.userProfileLoading = true;
            this.userProfileError = null;
            
            try {
                if (!this.agentId) {
                    this.userProfileError = '未获取到智能体ID';
                    return;
                }
                
                const response = await axios.post(AuthUtils.buildApiUrl(`/playground/v1/agent-memory/user-profile/${this.agentId}`));
                
                if (response.data && response.data.success) {
                    this.userProfileData = response.data.data;
                } else {
                    this.userProfileError = response.data?.errMessage || '获取用户画像失败';
                }
            } catch (error) {
                console.error('获取用户画像失败:', error);
                this.userProfileError = error.response?.data?.errMessage || error.message || '网络请求失败';
            } finally {
                this.userProfileLoading = false;
            }
        },

        // 刷新用户画像
        async refreshUserProfile() {
            await this.loadUserProfile();
        },

        // 处理弹窗关闭
        handleUserProfileDialogClose() {
            this.userProfileDialogVisible = false;
            this.userProfileData = null;
            this.userProfileError = null;
        },

        // 格式化JSON数据
        formatJsonData(data) {
            if (!data) return '';
            try {
                // 如果data是字符串，先尝试解析它
                let parsedData = data;
                if (typeof data === 'string') {
                    try {
                        parsedData = JSON.parse(data);
                    } catch (e) {
                        // 如果解析失败，直接返回原字符串
                        return data;
                    }
                }
                return JSON.stringify(parsedData, null, 2);
            } catch (error) {
                return String(data);
            }
        },

        // 复制用户画像数据
        copyProfileData() {
            if (!this.userProfileData) return;
            
            const textToCopy = this.formatJsonData(this.userProfileData);
            navigator.clipboard.writeText(textToCopy).then(() => {
                const { ElMessage } = ElementPlus;
                ElMessage.success('数据已复制到剪贴板');
            }).catch(() => {
                // 降级处理：使用传统方式复制
                const textarea = document.createElement('textarea');
                textarea.value = textToCopy;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                
                const { ElMessage } = ElementPlus;
                ElMessage.success('数据已复制到剪贴板');
            });
        },

        // 触发图片上传
        triggerImageUpload() {
            this.$refs.imageInput.click();
        },

        // 处理图片上传
        async handleImageUpload(event) {
            const files = event.target.files;
            if (!files || files.length === 0) return;
            
            const file = files[0];
            
            // 验证文件是否为图片
            if (!file.type.startsWith('image/')) {
                const { ElMessage } = ElementPlus;
                ElMessage.error('请选择图片文件');
                event.target.value = '';
                return;
            }

            const { ElMessage, ElLoading } = ElementPlus;
            const loadingInstance = ElLoading.service({
                target: '.chat-conversation-panel',
                text: '图片上传中...',
                background: 'rgba(255, 255, 255, 0.7)'
            });

            try {
                // 1. 获取OSS STS令牌
                const stsResponse = await axios.get(AuthUtils.buildApiUrl('/playground/v1/sts'));
                
                if (!stsResponse.data || !stsResponse.data.success) {
                    throw new Error(stsResponse.data?.errMessage || '获取上传凭证失败');
                }
                
                const stsData = stsResponse.data.data;
                
                // 2. 使用OSS SDK上传文件
                const client = new OSS({
                    region: stsData.regionId,
                    accessKeyId: stsData.accessKeyId,
                    accessKeySecret: stsData.accessKeySecret,
                    stsToken: stsData.securityToken,
                    bucket: stsData.bucket,
                    endpoint: stsData.ossEndpoint,
                });
                
                // 构建文件路径和名称
                const fileExt = file.name.substring(file.name.lastIndexOf('.'));
                const objectName = `${stsData.uploadPath}/${stsData.fileName}${fileExt}`;
                
                // 上传文件
                const result = await client.put(objectName, file);
                
                if (result && result.res.status === 200) {
                    // 构建访问URL - 使用domainName字段
                    const imageUrl = `${stsData.domainName}${objectName}`;
                    
                    this.uploadedImages.push({ 
                        url: imageUrl, 
                        name: file.name,
                        objectName: objectName // 保存对象名，便于后续处理
                    });
                    
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                    
                    ElMessage.success('图片上传成功');
                } else {
                    throw new Error('图片上传失败');
                }
            } catch (error) {
                console.error('图片上传失败:', error);
                ElMessage.error('图片上传失败: ' + (error.response?.data?.errMessage || error.message));
            } finally {
                loadingInstance.close();
                event.target.value = ''; // 清空文件输入框，允许再次选择
            }
        },

        // 移除图片
        removeImage(index) {
            this.uploadedImages.splice(index, 1);
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },

        // 处理全局ESC键
        handleGlobalKeydown(event) {
            if (event.key === 'Escape' || event.keyCode === 27) {
                // 关闭用户画像弹窗
                if (this.userProfileDialogVisible) {
                    this.userProfileDialogVisible = false;
                    event.preventDefault();
                    return;
                }
            }
        },

        // 预览图片
        previewImage(url) {
            // 创建一个自定义的图片预览覆盖层
            const overlay = document.createElement('div');
            overlay.className = 'custom-image-preview-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.85);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 3000;
                padding: 40px;
                transition: opacity 0.3s ease;
            `;

            // 创建图片容器
            const imageContainer = document.createElement('div');
            imageContainer.style.cssText = `
                max-width: 90%;
                max-height: 80%;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            // 创建图片元素
            const image = document.createElement('img');
            image.src = url;
            image.style.cssText = `
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            `;

            // 创建关闭按钮
            const closeButton = document.createElement('button');
            closeButton.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            `;
            closeButton.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: white;
                transition: background 0.2s ease;
            `;
            closeButton.addEventListener('mouseover', () => {
                closeButton.style.background = 'rgba(255, 255, 255, 0.3)';
            });
            closeButton.addEventListener('mouseout', () => {
                closeButton.style.background = 'rgba(255, 255, 255, 0.2)';
            });

            // 添加事件监听器
            closeButton.addEventListener('click', () => {
                document.body.removeChild(overlay);
            });
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    document.body.removeChild(overlay);
                }
            });

            // 组装并添加到DOM
            imageContainer.appendChild(image);
            overlay.appendChild(imageContainer);
            overlay.appendChild(closeButton);
            document.body.appendChild(overlay);

            // 添加键盘事件监听器
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(overlay);
                    document.removeEventListener('keydown', handleKeyDown);
                }
            };
            document.addEventListener('keydown', handleKeyDown);
        }
    }
};

// 导出组件
window.ChatConversationComponent = ChatConversationComponent; 
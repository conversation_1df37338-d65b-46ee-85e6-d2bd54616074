// 系统提示词组件
const SystemPromptComponent = {
    template: `
        <div class="system-prompt-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    智能体配置
                </h3>
                <div class="panel-actions">
                    <button class="action-btn" @click="savePrompt" title="保存">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17,21 17,13 7,13 7,21"></polyline>
                            <polyline points="7,3 7,8 15,8"></polyline>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="panel-content">
                <div class="model-select-container">
                    <label class="model-select-label">
                        <span>模型选择</span>
                    </label>
                    <el-select
                        v-model="selectedModel"
                        placeholder="请选择模型"
                        class="model-select"
                        size="default"
                        :loading="modelsLoading"
                        @change="onModelChange"
                    >
                        <el-option
                            v-for="model in modelList"
                            :key="model.value"
                            :label="model.name"
                            :value="model.value"
                        />
                    </el-select>
                </div>
                <div class="prompt-section">
                    <div class="prompt-section-title">系统提示词</div>
                    <!-- 系统提示词工具栏 -->
                    <div class="prompt-toolbar">
                        <div class="toolbar-left">
                            <button class="toolbar-btn" @click="openVariableDialog('system')" title="查看可用变量">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <path d="M12 17h.01"></path>
                                </svg>
                                变量
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <span class="char-count">{{ systemPrompt.length }} 字符</span>
                        </div>
                    </div>
                    <div class="prompt-editor-container">
                        <div class="prompt-editor-wrapper">
                            <textarea
                                v-model="systemPrompt"
                                class="prompt-editor"
                                :placeholder="getPlaceholderText()"
                                @input="onPromptChange"
                                @keydown="onKeyDown"
                                :disabled="loading"
                                ref="promptEditor"
                                style="min-height: 400px !important; max-height: 450px !important;"
                            ></textarea>
                        </div>
                    </div>
                </div>
                <div class="prompt-section">
                    <div class="prompt-section-title">用户提示词</div>
                    <!-- 用户提示词工具栏 -->
                    <div class="prompt-toolbar">
                        <div class="toolbar-left">
                            <button class="toolbar-btn" @click="openVariableDialog('user')" title="查看可用变量">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <path d="M12 17h.01"></path>
                                </svg>
                                变量
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <span class="char-count">{{ userPrompt.length }} 字符</span>
                        </div>
                    </div>
                    <div class="prompt-editor-container">
                        <div class="prompt-editor-wrapper">
                            <textarea
                                v-model="userPrompt"
                                class="prompt-editor"
                                :placeholder="getUserPromptPlaceholder()"
                                @input="onUserPromptChange"
                                @keydown="onKeyDown"
                                :disabled="loading"
                                ref="userPromptEditor"
                                style="min-height: 100px !important; max-height: 200px !important;"
                            ></textarea>
                        </div>
                    </div>
                </div>
                <div class="attribute-buttons">
                    <button class="attribute-btn" @click="openAttributeDialog('naturalAttributes')" :disabled="loading">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                        </svg>
                        自然属性
                    </button>
                    <button class="attribute-btn" @click="openAttributeDialog('temperament')" :disabled="loading">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path>
                            <line x1="16" y1="8" x2="2" y2="22"></line>
                            <line x1="17.5" y1="15" x2="9" y2="15"></line>
                        </svg>
                        气质人格
                    </button>
                    <button class="attribute-btn" @click="openAttributeDialog('personality')" :disabled="loading">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 12l2 2 4-4"></path>
                            <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                            <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                        </svg>
                        特质人格
                    </button>
                    <button class="attribute-btn" @click="openAttributeDialog('interaction')" :disabled="loading">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                        </svg>
                        交互设定
                    </button>
                </div>
            </div>

            <!-- 属性设置对话框 -->
            <el-dialog
                :model-value="dialogVisible"
                :title="dialogTitle"
                @update:model-value="closeDialog"
                width="600px"
                :close-on-click-modal="false"
                :close-on-press-escape="true"
                class="attribute-dialog"
            >
                <div class="attribute-dialog-content">
                    <el-input
                        v-model="dialogContent"
                        type="textarea"
                        :rows="15"
                        :placeholder="getAttributePlaceholder()"
                        class="attribute-textarea"
                        resize="none"
                    />
                    <div class="dialog-stats">
                        <div class="stat-item">
                            <span class="stat-label">字符数：</span>
                            <span class="stat-value">{{ dialogContent.length }}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">预估Token：</span>
                            <span class="stat-value">{{ Math.ceil(dialogContent.length / 2) }}</span>
                        </div>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="closeDialog">取消</el-button>
                        <el-button type="primary" @click="saveAttributeContent">保存</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 变量参考对话框 -->
            <el-dialog
                :model-value="variableDialogVisible"
                :title="variableDialogTitle"
                @update:model-value="closeVariableDialog"
                width="700px"
                :close-on-click-modal="false"
                :close-on-press-escape="true"
                class="variable-dialog"
            >
                <div class="variable-dialog-content">
                    <div class="variable-dialog-header">
                        <p class="variable-dialog-desc">
                            点击变量名可复制到剪贴板，点击插入按钮可插入到光标位置
                        </p>
                    </div>
                    <div class="variable-list">
                        <div 
                            v-for="variable in variableList" 
                            :key="variable.name"
                            class="variable-item"
                        >
                            <div class="variable-info">
                                <div class="variable-name" @click="copyVariable(variable.name)">{{ variable.name }}</div>
                                <div class="variable-desc">{{ variable.description }}</div>
                            </div>
                            <div class="variable-actions">
                                <el-button size="small" type="primary" @click="insertVariable(variable.name)" plain>
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M12 5v14M5 12h14"></path>
                                    </svg>
                                    插入
                                </el-button>
                                <el-button size="small" type="default" @click="copyVariable(variable.name)" plain>
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                    </svg>
                                    复制
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="closeVariableDialog">关闭</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    `,
    props: {
        agentId: String
    },
    data() {
        return {
            systemPrompt: '',
            userPrompt: '',
            selectedModel: '',
            modelList: [],
            modelsLoading: false,
            isModified: false,
            dialogVisible: false,
            dialogTitle: '',
            dialogContent: '',
            currentAttribute: null,
            attributeData: {
                naturalAttributes: '',
                temperament: '',
                personality: '',
                interaction: ''
            },
            loading: false,
            // 变量对话框相关
            variableDialogVisible: false,
            variableDialogTitle: '',
            currentPromptType: '', // 'system' 或 'user'
            // 可引用变量列表
            variableList: [
                { name: '{user_id}', description: '用户ID' },
                { name: '{agent_name}', description: '智能体名称' },
                { name: '{user_input}', description: '用户本次提问' },
                { name: '{conversation_id}', description: '会话ID' },
                { name: '{natural_attributes}', description: '自然属性设定' },
                { name: '{temperament_profile}', description: '气质人格设定' },
                { name: '{interaction_profile}', description: '交互设定' },
                { name: '{trait_profile}', description: '特质人格设定' },
                { name: '{chat_memory}', description: '最近N轮对话' },
                { name: '{agent_last_emotion}', description: '智能体上一轮情绪向量' },
                { name: '{user_emotion}', description: '本轮用户情绪向量（注：组件执行后才会有）' },
                { name: '{agent_emotion}', description: '本轮智能体情绪向量（注：组件执行后才会有）' },
                { name: '{user_profile}', description: '记忆库-用户画像' }
            ]
        };
    },
    methods: {
        getPlaceholderText() {
            if (this.loading) {
                return '正在加载Agent信息...';
            }
            return '请输入系统提示词，这将指导AI的行为和回答方式...\n\n例如：\n你是一个专业的AI助手，具有以下特点：\n1. 友好、耐心、专业\n2. 能够理解和回答各种问题\n3. 提供准确、有帮助的信息\n4. 遵循道德和安全准则\n\n请根据用户的问题提供有帮助的回答。';
        },
        getUserPromptPlaceholder() {
            if (this.loading) {
                return '正在加载Agent信息...';
            }
            return '请输入用户提示词，这将作为对话的默认前缀或上下文...\n\n例如：\n请以专业且友好的语气回答我的问题。\n或者：\n我是一个初学者，请用简单易懂的语言解释。';
        },
        onPromptChange() {
            this.isModified = true;
            this.$emit('prompt-change', this.systemPrompt);
        },
        onUserPromptChange() {
            this.isModified = true;
            this.$emit('user-prompt-change', this.userPrompt);
        },
        onModelChange(value) {
            this.selectedModel = value;
            this.isModified = true;
            console.log('模型选择更改:', value);
        },
        onKeyDown(event) {
            if (event.key === 'Tab') {
                event.preventDefault();
                const textarea = event.target;
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;

                const value = textarea.value;
                textarea.value = value.substring(0, start) + '    ' + value.substring(end);

                textarea.selectionStart = textarea.selectionEnd = start + 4;

                if (event.target === this.$refs.promptEditor) {
                    this.systemPrompt = textarea.value;
                    this.onPromptChange();
                } else if (event.target === this.$refs.userPromptEditor) {
                    this.userPrompt = textarea.value;
                    this.onUserPromptChange();
                }
            }
        },
        // 打开变量对话框
        openVariableDialog(type) {
            this.currentPromptType = type;
            this.variableDialogTitle = type === 'system' ? '系统提示词 - 可引用变量' : '用户提示词 - 可引用变量';
            this.variableDialogVisible = true;
        },
        
        // 关闭变量对话框
        closeVariableDialog() {
            this.variableDialogVisible = false;
            this.currentPromptType = '';
            this.variableDialogTitle = '';
        },
        
        // 插入变量到对应的textarea
        insertVariable(variableName) {
            const textarea = this.currentPromptType === 'system' ? this.$refs.promptEditor : this.$refs.userPromptEditor;
            if (textarea) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                
                if (this.currentPromptType === 'system') {
                    const content = this.systemPrompt;
                    this.systemPrompt = content.substring(0, start) + variableName + content.substring(end);
                    this.onPromptChange();
                } else {
                    const content = this.userPrompt;
                    this.userPrompt = content.substring(0, start) + variableName + content.substring(end);
                    this.onUserPromptChange();
                }
                
                // 重新设置光标位置
                this.$nextTick(() => {
                    textarea.focus();
                    textarea.setSelectionRange(start + variableName.length, start + variableName.length);
                });
            }
        },
        
        // 复制变量到剪贴板
        async copyVariable(variableName) {
            try {
                await navigator.clipboard.writeText(variableName);
                const { ElMessage } = ElementPlus;
                ElMessage.success(`已复制变量 ${variableName} 到剪贴板`);
            } catch (error) {
                console.error('复制失败:', error);
                // 降级处理：使用传统的复制方法
                const textArea = document.createElement('textarea');
                textArea.value = variableName;
                textArea.style.position = 'absolute';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    const { ElMessage } = ElementPlus;
                    ElMessage.success(`已复制变量 ${variableName} 到剪贴板`);
                } catch (fallbackError) {
                    console.error('降级复制也失败:', fallbackError);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('复制失败，请手动复制');
                } finally {
                    document.body.removeChild(textArea);
                }
            }
        },
        
        // 清空提示词
        clearPrompt(type) {
            if (type === 'system') {
                this.systemPrompt = '';
                this.onPromptChange();
                this.$nextTick(() => {
                    if (this.$refs.promptEditor) {
                        this.$refs.promptEditor.focus();
                    }
                });
            } else {
                this.userPrompt = '';
                this.onUserPromptChange();
                this.$nextTick(() => {
                    if (this.$refs.userPromptEditor) {
                        this.$refs.userPromptEditor.focus();
                    }
                });
            }
        },
        
        loadModelList() {
            this.modelsLoading = true;
            axios.post(AuthUtils.buildApiUrl('/playground/v1/model/list'), {})
                .then(response => {
                    if (response.data && response.data.success) {
                        this.modelList = response.data.data || [];
                        console.log('模型列表加载成功:', this.modelList);
                    } else {
                        console.error('获取模型列表失败:', response.data && response.data.errMessage);
                        const { ElMessage } = ElementPlus;
                        ElMessage.error('获取模型列表失败: ' + (response.data && response.data.errMessage || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('加载模型列表失败:', error);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('加载模型列表失败: ' + (error.response && error.response.data && error.response.data.errMessage || error.message));
                })
                .finally(() => {
                    this.modelsLoading = false;
                });
        },
        savePrompt() {
            if (!this.agentId) {
                const { ElMessage } = ElementPlus;
                ElMessage.error('智能体ID不能为空');
                return;
            }

            this.loading = true;

            const updateData = {
                agentId: this.agentId,
                systemPrompt: this.systemPrompt,
                userPrompt: this.userPrompt,
                chatModel: this.selectedModel
            };

            axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-template/update'), updateData)
                .then(response => {
                    if (response.data && response.data.success) {
                        const { ElMessage } = ElementPlus;
                        ElMessage.success('智能体配置已保存');
                        this.isModified = false;
                        console.log('智能体配置更新成功:', updateData);
                    } else {
                        console.error('更新智能体配置失败:', response.data && response.data.errMessage);
                        const { ElMessage } = ElementPlus;
                        ElMessage.error('保存失败: ' + (response.data && response.data.errMessage || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('更新智能体配置失败:', error);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('保存失败: ' + (error.response && error.response.data && error.response.data.errMessage || error.message));
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        openAttributeDialog(attribute) {
            this.currentAttribute = attribute;
            this.dialogContent = this.attributeData[attribute] || '';
            this.dialogTitle = this.getAttributeTitle(attribute);
            this.dialogVisible = true;
        },
        closeDialog() {
            this.dialogVisible = false;
            this.currentAttribute = null;
            this.dialogContent = '';
            this.dialogTitle = '';
        },
        saveAttributeContent() {
            if (this.currentAttribute) {
                // 先更新本地数据
                this.attributeData[this.currentAttribute] = this.dialogContent;
                
                // 调用后端接口更新
                this.updateAgentAttribute(this.currentAttribute, this.dialogContent);
            }
        },
        updateAgentAttribute(attribute, content) {
            if (!this.agentId) {
                const { ElMessage } = ElementPlus;
                ElMessage.error('智能体ID不能为空');
                return;
            }

            this.loading = true;
            
            // 构建更新数据，只更新当前修改的字段
            const updateData = {
                agentId: this.agentId
            };
            
            // 根据属性类型映射到后端字段
            switch (attribute) {
                case 'naturalAttributes':
                    updateData.naturalAttributes = content;
                    break;
                case 'temperament':
                    updateData.temperamentProfile = content;
                    break;
                case 'personality':
                    updateData.traitProfile = content;
                    break;
                case 'interaction':
                    updateData.interactionProfile = content;
                    break;
                default:
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('未知的属性类型');
                    this.loading = false;
                    return;
            }

            axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-template/update'), updateData)
                .then(response => {
                    if (response.data && response.data.success) {
                        const { ElMessage } = ElementPlus;
                        ElMessage.success(this.getAttributeTitle(attribute) + '已保存');
                        this.closeDialog();
                        console.log('Agent属性更新成功:', attribute, content);
                    } else {
                        console.error('更新Agent属性失败:', response.data && response.data.errMessage);
                        const { ElMessage } = ElementPlus;
                        ElMessage.error('保存失败: ' + (response.data && response.data.errMessage || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('更新Agent属性失败:', error);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('保存失败: ' + (error.response && error.response.data && error.response.data.errMessage || error.message));
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        getAttributeTitle(attribute) {
            const titles = {
                naturalAttributes: '自然属性',
                temperament: '气质人格',
                personality: '特质人格',
                interaction: '交互设定'
            };
            return titles[attribute] || '';
        },
        getAttributePlaceholder() {
            const placeholders = {
                naturalAttributes: '请输入自然属性设定...\n\n例如：\n• 年龄：25岁\n• 性别：女性\n• 职业：AI产品经理\n• 教育背景：计算机科学硕士\n• 兴趣爱好：阅读、音乐、旅行',
                temperament: '请输入气质人格设定...\n\n例如：\n• 性格特点：开朗、乐观、富有创造力\n• 情感表达：温暖、亲和、善于倾听\n• 思维方式：逻辑清晰、善于分析\n• 价值观：注重效率、追求卓越',
                personality: '请输入特质人格设定...\n\n例如：\n• 核心特质：责任心强、细心认真\n• 行为风格：主动积极、注重细节\n• 沟通风格：清晰明确、耐心解答\n• 决策风格：理性分析、权衡利弊',
                interaction: '请输入交互设定...\n\n例如：\n• 语言风格：专业而友好，避免过于正式\n• 回应方式：及时响应，提供具体建议\n• 互动原则：尊重用户、保护隐私\n• 特殊处理：遇到敏感话题时的应对方式'
            };
            return placeholders[this.currentAttribute] || '请输入内容...';
        },
        loadAgentInfo() {
            if (!this.agentId) {
                return;
            }

            this.loading = true;
            axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-template/info/' + this.agentId), {})
                .then(response => {
                    if (response.data && response.data.success) {
                        const agentData = response.data.data;

                        this.systemPrompt = agentData.systemPrompt || '';
                        this.userPrompt = agentData.userPrompt || '';
                        this.selectedModel = agentData.chatModel || '';

                        this.attributeData.naturalAttributes = agentData.naturalAttributes || '';
                        this.attributeData.temperament = agentData.temperamentProfile || '';
                        this.attributeData.personality = agentData.traitProfile || '';
                        this.attributeData.interaction = agentData.interactionProfile || '';

                        this.$emit('prompt-change', this.systemPrompt);
                        this.$emit('user-prompt-change', this.userPrompt);

                        console.log('Agent信息加载成功:', agentData);
                    } else {
                        console.error('获取Agent信息失败:', response.data && response.data.errMessage);
                        const { ElMessage } = ElementPlus;
                        ElMessage.error('获取Agent信息失败: ' + (response.data && response.data.errMessage || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('加载Agent信息失败:', error);
                    const { ElMessage } = ElementPlus;
                    ElMessage.error('加载Agent信息失败: ' + (error.response && error.response.data && error.response.data.errMessage || error.message));
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    },
    mounted() {
        this.loadModelList();
        this.loadAgentInfo();
    },
    watch: {
        agentId: {
            handler(newId) {
                if (newId) {
                    this.loadAgentInfo();
                }
            },
            immediate: false
        }
    }
};

// 导出组件
window.SystemPromptComponent = SystemPromptComponent; 
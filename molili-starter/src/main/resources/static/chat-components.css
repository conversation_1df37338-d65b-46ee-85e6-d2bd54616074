/* Element Plus 组件自定义样式 */
.el-select .el-input {
    height: 32px;
}

.el-select .el-input .el-input__inner {
    height: 32px;
    line-height: 32px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
}

.el-select .el-input .el-input__inner:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.el-select-dropdown {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
}

.el-select-dropdown .el-popper__arrow::before {
    border-bottom-color: #e5e7eb;
}

.el-select-dropdown .el-select-dropdown__item {
    height: auto;
    padding: 8px 12px;
    font-size: 14px;
    color: #374151;
    border-radius: 4px;
    margin: 2px 6px;
}

.el-select-dropdown .el-select-dropdown__item:hover {
    background-color: #f3f4f6;
}

.el-select-dropdown .el-select-dropdown__item.selected {
    background-color: #eff6ff;
    color: #3b82f6;
}

/* 滑块组件样式 */
.el-slider {
    height: 6px;
}

.el-slider .el-slider__runway {
    height: 6px;
    background-color: #f1f5f9;
    border-radius: 3px;
}

.el-slider .el-slider__bar {
    height: 6px;
    background-color: #3b82f6;
    border-radius: 3px;
}

.el-slider .el-slider__button {
    width: 16px;
    height: 16px;
    border: 2px solid #3b82f6;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-slider .el-slider__button:hover {
    transform: scale(1.1);
}

.el-slider .el-slider__button-wrapper {
    top: -5px;
}

/* 复选框组件样式 */
.el-checkbox {
    font-size: 14px;
    color: #374151;
}

.el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.el-checkbox .el-checkbox__inner {
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
}

.el-checkbox .el-checkbox__inner::after {
    height: 7px;
    left: 4px;
    top: 1px;
    width: 4px;
}

.el-checkbox .el-checkbox__label {
    color: #374151;
    font-weight: 500;
}

/* 消息框样式 */
.el-message {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.el-message.el-message--success {
    background-color: #f0f9f0;
    border-color: #10b981;
}

.el-message.el-message--error {
    background-color: #fef2f2;
    border-color: #ef4444;
}

.el-message.el-message--warning {
    background-color: #fffbeb;
    border-color: #f59e0b;
}

.el-message.el-message--info {
    background-color: #eff6ff;
    border-color: #3b82f6;
}

/* 确认对话框样式 */
.el-message-box {
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.el-message-box .el-message-box__header {
    padding: 20px 20px 10px;
}

.el-message-box .el-message-box__title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.el-message-box .el-message-box__content {
    padding: 10px 20px;
    color: #6b7280;
    font-size: 14px;
}

.el-message-box .el-message-box__btns {
    padding: 10px 20px 20px;
}

.el-message-box .el-button {
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
}

.el-message-box .el-button--primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.el-message-box .el-button--primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.el-message-box .el-button--default {
    background-color: #ffffff;
    border-color: #e5e7eb;
    color: #374151;
}

.el-message-box .el-button--default:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

/* 工具提示样式 */
.el-tooltip__popper {
    background-color: #374151;
    color: #ffffff;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-tooltip__popper .popper__arrow {
    border-top-color: #374151;
}

.el-tooltip__popper .popper__arrow::after {
    border-top-color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .el-select .el-input {
        height: 36px;
    }
    
    .el-select .el-input .el-input__inner {
        height: 36px;
        line-height: 36px;
        font-size: 16px;
    }
    
    .el-checkbox {
        font-size: 16px;
    }
    
    .el-checkbox .el-checkbox__inner {
        width: 18px;
        height: 18px;
    }
    
    .el-slider .el-slider__button {
        width: 18px;
        height: 18px;
    }
    
    .el-slider .el-slider__button-wrapper {
        top: -6px;
    }
}

/* 下拉菜单滚动条样式 */
.el-select-dropdown .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
}

.el-select-dropdown .el-scrollbar .el-scrollbar__bar {
    opacity: 0.6;
}

.el-select-dropdown .el-scrollbar .el-scrollbar__thumb {
    background-color: #d1d5db;
    border-radius: 4px;
}

.el-select-dropdown .el-scrollbar .el-scrollbar__thumb:hover {
    background-color: #9ca3af;
}

/* 组件加载状态 */
.component-loading {
    position: relative;
    min-height: 100px;
}

.component-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.component-loading-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 组件错误状态 */
.component-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.component-error-icon {
    font-size: 48px;
    color: #ef4444;
    margin-bottom: 16px;
}

.component-error-message {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 16px;
}

.component-error-retry {
    background-color: #3b82f6;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.component-error-retry:hover {
    background-color: #2563eb;
}

/* 组件空状态 */
.component-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.component-empty-icon {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
}

.component-empty-message {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 16px;
}

.component-empty-action {
    background-color: #f3f4f6;
    color: #374151;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.component-empty-action:hover {
    background-color: #e5e7eb;
}

/* 洞察信息运行过程样式 */
.insight-process-container {
    margin-bottom: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #ffffff;
    overflow: hidden;
}

.insight-process-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.insight-process-header:hover {
    background-color: #f1f5f9;
}

.insight-process-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #10b981;
    color: #ffffff;
    margin-right: 12px;
    flex-shrink: 0;
}

.insight-process-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    flex: 1;
}

.insight-process-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.insight-process-stats {
    font-size: 12px;
    color: #6b7280;
}

.insight-process-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    color: #6b7280;
    transition: transform 0.2s ease;
}

.insight-process-toggle.expanded {
    transform: rotate(180deg);
}

.insight-process-content {
    padding: 16px;
}

.insight-process-summary {
    margin-bottom: 16px;
}

.process-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #10b981;
}

.process-status.completed {
    color: #10b981;
}

.process-status.running {
    color: #3b82f6;
}

.process-status.failed {
    color: #ef4444;
}

.status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #dcfce7;
    color: #10b981;
    flex-shrink: 0;
}

.process-status.running .status-icon {
    background-color: #dbeafe;
    color: #3b82f6;
}

.process-status.failed .status-icon {
    background-color: #fee2e2;
    color: #ef4444;
}

.process-time {
    margin-left: auto;
    font-size: 12px;
    color: #6b7280;
}

.insight-items {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.insight-item {
    border: 1px solid #f3f4f6;
    border-radius: 6px;
    background-color: #ffffff;
    overflow: hidden;
    transition: border-color 0.2s ease;
}

.insight-item:hover {
    border-color: #e5e7eb;
}

.insight-item.expanded {
    border-color: #e5e7eb;
}

.insight-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.insight-item-header:hover {
    background-color: #f9fafb;
}

.insight-item-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.insight-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #10b981;
    color: #ffffff;
    flex-shrink: 0;
}

.insight-item-title {
    font-size: 13px;
    font-weight: 500;
    color: #374151;
}

.insight-item-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.insight-item-time {
    font-size: 12px;
    color: #6b7280;
}

.insight-item-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    color: #9ca3af;
    transition: transform 0.2s ease, color 0.2s ease;
}

.insight-item-toggle.expanded {
    transform: rotate(180deg);
    color: #6b7280;
}

.insight-item-content {
    border-top: 1px solid #f3f4f6;
    padding: 12px;
    background-color: #fafbfc;
}

.insight-content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.insight-content-text {
    font-size: 13px;
    color: #4b5563;
    line-height: 1.5;
    white-space: pre-wrap;
}

.insight-metadata {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding-top: 8px;
    border-top: 1px solid #f3f4f6;
}

.metadata-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.metadata-key {
    font-weight: 500;
    color: #6b7280;
    min-width: 60px;
}

.metadata-value {
    color: #374151;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .insight-process-header {
        padding: 10px 12px;
    }
    
    .insight-process-content {
        padding: 12px;
    }
    
    .insight-item-header {
        padding: 8px 10px;
    }
    
    .insight-item-content {
        padding: 10px;
    }
    
    .insight-process-title {
        font-size: 13px;
    }
    
    .insight-process-stats {
        font-size: 11px;
    }
    
    .insight-item-title {
        font-size: 12px;
    }
    
    .insight-item-time {
        font-size: 11px;
    }
    
    .insight-content-text {
        font-size: 12px;
    }
}

/* 模型选择下拉框样式 */
.model-select-container {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 8px;
    padding: 0;
    background-color: transparent;
    border: none;
    border-radius: 0;
    transition: none;
}



.model-select-label {
    display: block;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.5;
}

.model-select-label span {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.model-select {
    width: 100%;
    height: 32px;
}

.model-select .el-input__wrapper {
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.model-select .el-input__wrapper:hover {
    border-color: #d1d5db;
}

.model-select .el-input__wrapper.is-focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.model-select .el-input__inner {
    height: 30px;
    line-height: 30px;
    font-size: 13px;
    color: #374151;
    padding: 0 28px 0 10px;
}

.model-select .el-input__inner::placeholder {
    color: #9ca3af;
}

.model-select .el-input__suffix {
    right: 8px;
}

.model-select .el-input__suffix .el-input__suffix-inner {
    color: #6b7280;
}

.model-select .el-input__suffix .el-input__suffix-inner:hover {
    color: #374151;
}

/* 模型选择下拉框加载状态 */
.model-select.is-loading .el-input__suffix .el-input__suffix-inner {
    color: #3b82f6;
}

/* 响应式设计 - 模型选择 */
@media (max-width: 768px) {
    .model-select-container {
        margin-bottom: 16px;
        padding: 0;
    }
    
    .model-select-label {
        font-size: 13px;
        margin-bottom: 0;
    }
    
    .model-select {
        height: 36px;
    }
    
    .model-select .el-input__inner {
        height: 34px;
        line-height: 34px;
        font-size: 13px;
        padding: 0 24px 0 10px;
    }
    
    .model-select .el-input__suffix {
        right: 6px;
    }
}

/* 历史消息加载指示器样式 */
.loading-more-indicator,
.loading-history-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 20px;
    margin-bottom: 16px;
    background-color: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    animation: fadeIn 0.3s ease-out;
}

.loading-history-indicator {
    margin-top: 20px;
    margin-bottom: 20px;
    background-color: #eff6ff;
    border-color: #dbeafe;
    color: #3b82f6;
}

.loading-more-indicator .loading-spinner,
.loading-history-indicator .loading-spinner {
    width: 16px;
    height: 16px;
    border-width: 2px;
    margin: 0;
}

.loading-history-indicator .loading-spinner {
    border-color: #dbeafe;
    border-top-color: #3b82f6;
}

/* 响应式设计 - 加载指示器 */
@media (max-width: 768px) {
    .loading-more-indicator,
    .loading-history-indicator {
        padding: 12px 16px;
        margin-bottom: 12px;
        font-size: 13px;
    }
    
    .loading-history-indicator {
        margin-top: 16px;
        margin-bottom: 16px;
    }
    
    .loading-more-indicator .loading-spinner,
    .loading-history-indicator .loading-spinner {
        width: 14px;
        height: 14px;
        border-width: 1.5px;
    }
}

/* 聊天消息样式 */
.message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: fadeIn 0.3s ease-out;
    margin-bottom: 16px;
}

.message.user {
    justify-content: flex-end;
}

.message.user .message-avatar {
    order: 2;
}

.message.user .message-body {
    order: 1;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message.assistant .message-avatar {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
}

.message-body {
    max-width: 70%;
}

.message-content {
    padding: 12px 16px;
    border-radius: 12px;
    line-height: 1.5;
    font-size: 14px;
    position: relative;
    word-wrap: break-word;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.message-text {
    flex: 1;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background: #ffffff;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 12px;
    color: #6b7280;
    margin-top: 6px;
    padding: 2px 6px;
    font-weight: 400;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.message.user .message-time {
    text-align: right;
}

.message-time:hover {
    opacity: 1;
    color: #374151;
}

.message:hover .message-time {
    opacity: 1;
}

.loading-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-top: 1px;
    flex-shrink: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 流式加载样式 */
.message-content.streaming {
    position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-body {
        max-width: 85%;
    }
    
    .message-content {
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .message-avatar {
        width: 24px;
        height: 24px;
        font-size: 11px;
    }
    
    .message-time {
        font-size: 11px;
    }
}

/* 用户画像弹窗样式 */
.user-profile-dialog .el-dialog {
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.user-profile-dialog .el-dialog__header {
    padding: 20px 20px 10px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 12px 12px 0 0;
}

.user-profile-dialog .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-profile-dialog .el-dialog__body {
    padding: 0;
}

.user-profile-content {
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6b7280;
    gap: 12px;
}

.loading-container .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.profile-json-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.profile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
}

.profile-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.copy-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #e5e7eb;
    background: #ffffff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
}

.copy-btn:hover {
    background: #f3f4f6;
    border-color: #3b82f6;
    color: #3b82f6;
}

.profile-json-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.profile-json-content {
    flex: 1;
    padding: 20px;
    margin: 0;
    background: #ffffff;
    color: #374151;
    font-size: 13px;
    line-height: 1.5;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    white-space: pre-wrap;
    overflow-y: auto;
    border: none;
    outline: none;
    resize: none;
    max-height: 500px;
    border-radius: 0;
}

.profile-json-content::-webkit-scrollbar {
    width: 6px;
}

.profile-json-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.profile-json-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.profile-json-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.profile-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6b7280;
    text-align: center;
}

.error-icon {
    margin-bottom: 12px;
    color: #ef4444;
}

.error-text {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
    max-width: 400px;
}

.user-profile-dialog .el-dialog__footer {
    padding: 12px 20px 20px;
    background: #f8fafc;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e5e7eb;
}

.user-profile-dialog .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.user-profile-dialog .el-button {
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    height: 36px;
}

.user-profile-dialog .el-button--default {
    background-color: #ffffff;
    border-color: #e5e7eb;
    color: #374151;
}

.user-profile-dialog .el-button--default:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

.user-profile-dialog .el-button--primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.user-profile-dialog .el-button--primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-profile-dialog .el-dialog {
        width: 90% !important;
        margin: 0 auto;
    }
    
    .profile-json-content {
        font-size: 12px;
        padding: 16px;
        max-height: 300px;
    }
    
    .profile-header {
        padding: 12px 16px;
    }
    
    .loading-container {
        padding: 40px 16px;
    }
    
    .profile-error {
        padding: 40px 16px;
    }
}

/* 变量参考弹窗样式 */
.variable-reference-content {
    max-height: 500px;
    overflow-y: auto;
}

.variable-reference-header {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.variable-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.variable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
}

.variable-item:hover {
    background-color: #f8fafc;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.variable-info {
    flex: 1;
}

.variable-name {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.variable-desc {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
}

.variable-action {
    margin-left: 12px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.variable-item:hover .variable-action {
    opacity: 1;
}

.variable-action .el-button {
    padding: 4px 8px;
    color: #6b7280;
    border: none;
    background: none;
}

.variable-action .el-button:hover {
    color: #3b82f6;
    background-color: #eff6ff;
}

/* 设置标签与操作按钮的布局 */
.setting-label-with-action {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.setting-label-with-action .setting-label {
    margin-bottom: 0;
}

.setting-label-with-action .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    text-decoration: none;
}

.setting-label-with-action .el-button:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .variable-item {
        padding: 10px 12px;
    }
    
    .variable-name {
        font-size: 13px;
    }
    
    .variable-desc {
        font-size: 12px;
    }
    
    .variable-action {
        margin-left: 8px;
    }
}

/* 提示词编辑弹窗样式 */
.prompt-editor-content {
    padding: 0;
}

.prompt-editor-layout {
    display: flex;
    gap: 20px;
    height: 500px;
}

.prompt-editor-left {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.prompt-editor-right {
    width: 300px;
    border-left: 1px solid #e5e7eb;
    padding-left: 20px;
    overflow-y: auto;
}

.prompt-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.prompt-editor-label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.prompt-editor-actions {
    display: flex;
    gap: 8px;
}

.prompt-editor-actions .el-button {
    padding: 4px 8px;
    font-size: 12px;
    color: #6b7280;
    border: none;
    background: none;
}

.prompt-editor-actions .el-button:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.prompt-editor-textarea {
    flex: 1;
}

.prompt-editor-textarea .el-textarea__inner {
    height: 100% !important;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.prompt-editor-right .variable-reference-header h4 {
    display: flex;
    align-items: center;
    gap: 6px;
}

.prompt-editor-right .variable-list {
    max-height: 400px;
    overflow-y: auto;
}

.prompt-editor-right .variable-item {
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    cursor: default;
}

.prompt-editor-right .variable-item:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.prompt-editor-right .variable-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.prompt-editor-right .variable-name {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 12px;
    font-weight: 500;
    color: #3b82f6;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    display: inline-block;
    width: fit-content;
}

.prompt-editor-right .variable-name:hover {
    background-color: #dbeafe;
    border-color: #93c5fd;
}

.prompt-editor-right .variable-desc {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
}

.prompt-editor-right .variable-actions {
    display: flex;
    gap: 4px;
    margin-top: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.prompt-editor-right .variable-item:hover .variable-actions {
    opacity: 1;
}

.prompt-editor-right .variable-actions .el-button {
    padding: 2px 4px;
    font-size: 12px;
    color: #6b7280;
    border: none;
    background: none;
    border-radius: 3px;
}

.prompt-editor-right .variable-actions .el-button:hover {
    background-color: #e5e7eb;
    color: #374151;
}

/* 弹窗样式优化 */
.prompt-editor-content .el-dialog {
    border-radius: 12px;
}

.prompt-editor-content .el-dialog__header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e5e7eb;
}

.prompt-editor-content .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.prompt-editor-content .el-dialog__body {
    padding: 20px 24px;
}

.prompt-editor-content .el-dialog__footer {
    padding: 12px 24px 20px;
    border-top: 1px solid #e5e7eb;
}

.prompt-editor-content .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.prompt-editor-content .el-button {
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
}

.prompt-editor-content .el-button--primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.prompt-editor-content .el-button--primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.prompt-editor-content .el-button--default {
    background-color: #ffffff;
    border-color: #e5e7eb;
    color: #374151;
}

.prompt-editor-content .el-button--default:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .prompt-editor-layout {
        flex-direction: column;
        height: auto;
    }
    
    .prompt-editor-left {
        margin-bottom: 20px;
    }
    
    .prompt-editor-right {
        width: 100%;
        border-left: none;
        border-top: 1px solid #e5e7eb;
        padding-left: 0;
        padding-top: 20px;
    }
    
    .prompt-editor-textarea .el-textarea__inner {
        height: 200px !important;
    }
    
    .prompt-editor-right .variable-list {
        max-height: 200px;
    }
}

/* SystemPromptComponent 简洁样式 */
.system-prompt-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #ffffff;
}

.system-prompt-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafbfc;
}

.system-prompt-panel .panel-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #1f2937;
}

.system-prompt-panel .panel-title svg {
    color: #6b7280;
}

.system-prompt-panel .panel-actions {
    display: flex;
    gap: 8px;
}

.system-prompt-panel .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #ffffff;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.system-prompt-panel .action-btn:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
}

.system-prompt-panel .panel-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 12px;
    gap: 8px;
    overflow-y: auto;
}

/* 提示词区域样式 */
.prompt-section {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.prompt-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
}

.prompt-editor-container {
    position: relative;
}

.prompt-editor-wrapper {
    position: relative;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    transition: all 0.2s ease;
    overflow: hidden;
}

.prompt-editor-wrapper:hover {
    border-color: #d1d5db;
}

.prompt-editor-wrapper:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.prompt-editor {
    width: 100%;
    min-height: 80px;
    padding: 8px 10px;
    border: none;
    background-color: transparent;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #1f2937;
    outline: none;
    transition: all 0.2s ease;
}

.prompt-editor:focus {
    background-color: #ffffff;
}

.prompt-editor::placeholder {
    color: #9ca3af;
}

/* 用户提示词区域特殊样式 */
.prompt-section:last-of-type .prompt-editor {
    min-height: 40px !important;
    max-height: 60px !important;
    overflow-y: auto;
}

/* 属性按钮样式 */
.attribute-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.attribute-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    background-color: #ffffff;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.attribute-btn:hover {
    border-color: #d1d5db;
    color: #374151;
    background-color: #f9fafb;
}

.attribute-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.attribute-btn svg {
    flex-shrink: 0;
}

/* 属性对话框样式 */
.attribute-dialog .el-dialog {
    border-radius: 8px;
    overflow: hidden;
}

.attribute-dialog .el-dialog__header {
    background-color: #fafbfc;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.attribute-dialog .el-dialog__title {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.attribute-dialog .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
}

.attribute-dialog .el-dialog__headerbtn .el-dialog__close {
    color: #6b7280;
    font-size: 16px;
}

.attribute-dialog .el-dialog__body {
    padding: 20px;
}

.attribute-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.attribute-textarea .el-textarea__inner {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.attribute-textarea .el-textarea__inner:focus {
    border-color: #3b82f6;
    background-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dialog-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9fafb;
    border-radius: 6px;
    font-size: 12px;
    color: #6b7280;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-weight: 500;
}

.stat-value {
    font-weight: 600;
    color: #374151;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.dialog-footer .el-button {
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.dialog-footer .el-button--primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.dialog-footer .el-button--primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.dialog-footer .el-button--default {
    background-color: #ffffff;
    border-color: #e5e7eb;
    color: #374151;
}

.dialog-footer .el-button--default:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

/* 提示词工具栏样式 */
.prompt-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px 6px 0 0;
    margin-bottom: -1px;
}

.toolbar-left {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
    color: #475569;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.char-count {
    font-size: 12px;
    color: #94a3b8;
    font-weight: 500;
}

/* 变量对话框样式 */
.variable-dialog .el-dialog {
    border-radius: 12px;
}

.variable-dialog .el-dialog__body {
    padding: 0;
    max-height: none;
    overflow: hidden;
}

.variable-dialog-content {
    height: 500px;
    display: flex;
    flex-direction: column;
}

.variable-dialog-header {
    padding: 16px 24px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    flex-shrink: 0;
}

.variable-dialog-desc {
    margin: 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
}

.variable-dialog .variable-list {
    flex: 1;
    overflow-y: auto;
    background-color: #ffffff;
}

.variable-dialog .variable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.variable-dialog .variable-item:last-child {
    border-bottom: none;
}

.variable-dialog .variable-item:hover {
    background-color: #f8fafc;
}

.variable-dialog .variable-info {
    flex: 1;
    min-width: 0;
    margin-right: 16px;
}

.variable-dialog .variable-name {
    font-size: 14px;
    font-weight: 600;
    color: #3b82f6;
    cursor: pointer;
    transition: color 0.2s ease;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    margin-bottom: 4px;
}

.variable-dialog .variable-name:hover {
    color: #1d4ed8;
}

.variable-dialog .variable-desc {
    font-size: 13px;
    color: #64748b;
    line-height: 1.4;
}

.variable-dialog .variable-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.variable-dialog .variable-actions .el-button {
    height: 32px;
    padding: 0 12px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.2s ease;
}

/* 滚动条样式 */
.variable-dialog .variable-list::-webkit-scrollbar {
    width: 6px;
}

.variable-dialog .variable-list::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.variable-dialog .variable-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.variable-dialog .variable-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 提示词编辑器样式调整 */
.prompt-editor-container .prompt-editor-wrapper {
    border-radius: 0 0 6px 6px;
}

.prompt-editor-container .prompt-editor-wrapper .prompt-editor {
    border-radius: 0 0 6px 6px;
    border-top: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .system-prompt-panel .panel-content {
        padding: 10px;
        gap: 6px;
    }
    
    .prompt-section-title {
        font-size: 13px;
    }
    
    .prompt-editor {
        min-height: 80px;
        padding: 10px;
        font-size: 13px;
    }
    
    .prompt-section:last-of-type .prompt-editor {
        min-height: 20px !important;
    }
    
    .attribute-buttons {
        gap: 3px;
        margin-top: 3px;
    }
    
    .attribute-btn {
        font-size: 11px;
        padding: 4px 8px;
    }
    
    .attribute-dialog .el-dialog__body {
        padding: 16px;
    }
    
    .dialog-stats {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .prompt-toolbar {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
    
    .toolbar-left {
        justify-content: flex-start;
    }
    
    .toolbar-btn {
        font-size: 11px;
        padding: 6px 8px;
    }
    
    .variable-dialog .variable-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }
    
    .variable-dialog .variable-info {
        margin-right: 0;
        width: 100%;
    }
    
    .variable-dialog .variable-actions {
        align-self: flex-end;
    }
    
    .variable-dialog-content {
        height: 400px;
    }
    
    .variable-dialog .el-dialog {
        width: 90% !important;
        margin: 5vh auto;
    }
}

/* 图片预览区域样式 */
.image-preview-area {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.image-preview-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.image-preview-item:hover .preview-image {
    transform: scale(1.05);
}

.remove-image-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.remove-image-btn:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.7);
}

/* 消息中的图片样式 */
.message-images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.message-image-container {
    position: relative;
    max-width: 200px;
    max-height: 200px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-image {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.message-image:hover {
    transform: scale(1.02);
}
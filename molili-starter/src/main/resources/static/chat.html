<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体工作台 - Molili AI</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.1.0/dist/index.iife.min.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios@1.6.2/dist/axios.min.js"></script>
    <!-- <PERSON><PERSON> OSS SDK -->
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js"></script>
    <!-- Auth Utils -->
    <script src="auth.js"></script>
    <!-- Chat Components -->
    <script src="chat-components.js"></script>
    <!-- Component Styles -->
    <link rel="stylesheet" href="chat-components.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: #f7f8fa;
            height: 100vh;
            color: #333;
            overflow: hidden;
        }

        .chat-workspace {
            display: flex;
            height: 100vh;
            width: 100%;
        }

        /* 顶部工具栏 */
        .workspace-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            z-index: 1000;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .back-button {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #6b7280;
        }

        .back-button:hover {
            background: #f3f4f6;
            color: #1f2937;
        }

        .workspace-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .workspace-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-left: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .workspace-subtitle:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .edit-icon {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .workspace-subtitle:hover .edit-icon {
            opacity: 1;
        }

        .agent-name-container {
            display: flex;
            align-items: center;
            margin-left: 8px;
        }

        .agent-name-input {
            font-size: 14px;
            color: #374151;
            background: #ffffff;
            border: 1px solid #3b82f6;
            border-radius: 4px;
            padding: 4px 8px;
            outline: none;
            min-width: 120px;
            max-width: 200px;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .agent-name-input:focus {
            border-color: #2563eb;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            height: 36px;
            padding: 0 16px;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #374151;
        }

        .header-btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }

        .header-btn.primary {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .header-btn.primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        /* 用户信息卡片样式 */
        .user-card {
            display: flex;
            align-items: center;
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: 1px solid transparent;
        }

        .user-card:hover {
            background: #f9fafb;
            border-color: #e5e7eb;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            margin-right: 12px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .user-info {
            display: flex;
            flex-direction: column;
            margin-right: 12px;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.2;
        }

        .user-role {
            font-size: 12px;
            color: #6b7280;
            margin-top: 2px;
        }

        .user-menu-btn {
            background: none;
            border: none;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .user-menu-btn:hover {
            color: #3b82f6;
            background: #f3f4f6;
        }

        /* 主要内容区域 */
        .workspace-content {
            display: flex;
            width: 100%;
            height: 100vh;
            padding-top: 56px;
        }

        /* 左侧面板 - 系统提示词 */
        .left-panel {
            width: 520px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            min-width: 480px;
            max-width: 600px;
            resize: horizontal;
            overflow: hidden;
        }

        /* 中间面板 - 流程配置 */
        .middle-panel {
            width: 380px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            min-width: 340px;
            max-width: 420px;
            resize: horizontal;
            overflow: hidden;
        }

        /* 右侧面板 - 聊天对话 */
        .right-panel {
            flex: 1;
            background: #ffffff;
            display: flex;
            flex-direction: column;
            min-width: 400px;
        }

        /* 面板通用样式 */
        .panel-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fafbfc;
            min-height: 56px;
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .panel-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: transparent;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #6b7280;
            outline: none;
        }

        .action-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .action-btn:focus {
            outline: none;
            box-shadow: none;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .panel-content {
            flex: 1;
            padding: 16px 20px;
            overflow-y: auto;
            background: #ffffff;
        }

        /* 系统提示词面板样式 */
        .system-prompt-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .prompt-editor {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .prompt-textarea {
            flex: 1;
            width: 100%;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.6;
            resize: none;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            background: #fafbfc;
            color: #374151;
            min-height: 450px;
            tab-size: 4;
        }

        .prompt-editor-container {
            position: relative;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .prompt-editor-wrapper {
            position: relative;
            flex: 1;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafbfc;
            overflow: hidden;
        }



        .prompt-editor {
            width: 100%;
            min-height: 450px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.6;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: transparent;
            color: #374151;
            border: none;
            outline: none;
            resize: none;
            tab-size: 4;
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }

        .prompt-editor:focus {
            outline: none;
        }

        .prompt-editor-wrapper:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }





        .prompt-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .prompt-textarea::placeholder {
            color: #9ca3af;
        }

        .prompt-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #f1f3f4;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #6b7280;
        }

        .stat-value {
            font-weight: 500;
            color: #374151;
        }

        /* 属性按钮样式 */
        .attribute-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #f1f3f4;
        }

        .attribute-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            color: #374151;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 36px;
        }

        .attribute-btn:hover {
            background: #f8fafc;
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .attribute-btn:active {
            transform: translateY(0);
        }

        .attribute-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* 弹窗样式 */
        .attribute-dialog .el-dialog__header {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }

        .attribute-dialog .el-dialog__title {
            font-weight: 600;
            color: #1f2937;
        }

        .attribute-dialog-content {
            padding: 0;
        }

        .attribute-textarea {
            margin-bottom: 12px;
        }

        .attribute-textarea .el-textarea__inner {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
            background: #fafbfc;
            color: #374151;
            resize: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }

        .attribute-textarea .el-textarea__inner:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .dialog-stats {
            display: flex;
            justify-content: space-between;
            padding: 12px;
            background: #f8fafc;
            border-radius: 6px;
            border: 1px solid #f1f3f4;
        }

        .dialog-footer {
            display: flex;
            gap: 8px;
        }

        /* 流程配置面板样式 */
        .process-config-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .setting-group {
            margin-bottom: 24px;
        }

        .setting-label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .setting-value {
            font-size: 12px;
            color: #6b7280;
            font-weight: 400;
        }

        .setting-info {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: help;
            color: #9ca3af;
        }

        .setting-select {
            width: 100%;
        }

        .setting-slider {
            width: 100%;
            margin: 12px 0;
        }

        .setting-hint {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 4px;
        }

        .setting-checkbox {
            margin-bottom: 4px;
        }

        .model-option {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .model-name {
            font-weight: 500;
            color: #374151;
        }

        .model-desc {
            font-size: 12px;
            color: #9ca3af;
        }

        .settings-actions {
            display: flex;
            gap: 8px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f1f3f4;
        }

        .reset-btn, .save-btn {
            flex: 1;
            height: 32px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            background: #ffffff;
            color: #6b7280;
        }

        .reset-btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }

        .save-btn {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .save-btn:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        /* 流程配置组件样式 */
        .config-section {
            margin-bottom: 28px;
        }

        .section-header {
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f1f3f4;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .section-content .config-item {
            flex-shrink: 0;
        }

        .config-item {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.2s ease;
        }

        .config-item:hover {
            border-color: #d1d5db;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .config-item-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .config-item-title {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .config-item-desc {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .config-item-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .memory-recall-item .config-item-desc {
            margin-bottom: 8px;
            word-break: break-word;
            hyphens: auto;
            line-height: 1.4;
            min-height: 28px;
            max-height: 28px;
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
        }

        .memory-recall-item {
            transition: none;
            position: relative;
            padding: 12px 16px;
            min-height: 60px;
        }

        .config-item-settings {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e5e7eb;
        }

        .setting-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
        }

        .setting-row .setting-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            min-width: 60px;
        }

        .setting-row .setting-select {
            flex: 1;
            max-width: 120px;
        }

        .setting-row .setting-slider {
            flex: 1;
            max-width: 100px;
        }

        .placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 32px 16px;
            color: #9ca3af;
            text-align: center;
        }

        .placeholder-icon {
            margin-bottom: 12px;
            opacity: 0.5;
        }

        .placeholder-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .placeholder-title {
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
        }

        .placeholder-desc {
            font-size: 12px;
            color: #9ca3af;
        }

        .config-actions {
            display: flex;
            gap: 8px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f1f3f4;
        }

        /* 新增的样式 */
        .setting-group {
            margin-bottom: 16px;
        }

        .setting-group .setting-label {
            display: block;
            margin-bottom: 8px;
            font-size: 13px;
            font-weight: 500;
            color: #374151;
        }

        .setting-textarea {
            width: 100%;
        }

        .setting-textarea .el-textarea__inner {
            font-size: 12px;
            line-height: 1.5;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .setting-textarea .el-textarea__inner:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .output-variable-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .setting-actions {
            display: flex;
            gap: 8px;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #f1f3f4;
            justify-content: flex-end;
        }

        /* 聊天对话面板样式 */
        .chat-conversation-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .agent-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .agent-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .agent-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .agent-status {
            font-size: 12px;
            color: #10b981;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #10b981;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f7f8fa;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
            max-width: 500px;
            margin: 0 auto;
        }

        .welcome-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .welcome-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .welcome-description {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .welcome-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .suggestion-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .suggestion-card:hover {
            background: #f8fafc;
            border-color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .suggestion-card.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .suggestion-icon {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .suggestion-text {
            font-size: 12px;
            color: #374151;
            font-weight: 500;
        }

        .message {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            animation: fadeIn 0.3s ease-out;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.user .message-avatar {
            order: 2;
        }

        .message.user .message-body {
            order: 1;
        }

        .message-avatar {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #8b4513;
        }

        .message-body {
            max-width: 70%;
        }

        .message-content {
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
            font-size: 14px;
            position: relative;
            word-wrap: break-word;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .message-text {
            flex: 1;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: #ffffff;
            color: #374151;
            border: 1px solid #e5e7eb;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 12px;
            color: #6b7280;
            margin-top: 6px;
            padding: 2px 6px;
            font-weight: 400;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .message.user .message-time {
            text-align: right;
        }

        .message-time:hover {
            opacity: 1;
            color: #374151;
        }

        .message:hover .message-time {
            opacity: 1;
        }

        .loading-spinner {
            display: inline-block;
            width: 14px;
            height: 14px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-top: 1px;
            flex-shrink: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 洞察气泡样式 */
        .insight-bubble {
            background: #f5f6fa;
            border: 1px solid #d0d7de;
            border-radius: 8px;
            padding: 10px 12px;
            margin-bottom: 6px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.3s ease-out;
            border-left: 3px solid #8b949e;
        }

        .insight-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;
        }

        .insight-title {
            font-weight: 600;
            font-size: 12px;
            color: #656d76;
        }

        .insight-toggle {
            margin-left: auto;
            background: none;
            border: none;
            color: #8b949e;
            cursor: pointer;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .insight-toggle:hover {
            background: #e1e4e8;
            color: #656d76;
        }

        .insight-content {
            font-size: 11px;
            color: #656d76;
            line-height: 1.4;
            word-wrap: break-word;
            white-space: pre-wrap;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        /* 输入区域样式 */
        .input-area {
            padding: 16px 20px;
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
        }

        .input-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            transition: all 0.2s ease;
        }

        .input-wrapper:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-field {
            flex: 1;
            min-height: 20px;
            max-height: 100px;
            border: none;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            font-family: inherit;
            background: transparent;
            color: #1f2937;
            padding: 0;
        }

        .input-field:focus {
            outline: none;
        }

        .input-field::placeholder {
            color: #9ca3af;
        }

        .send-button {
            width: 32px;
            height: 32px;
            border: none;
            background: #3b82f6;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .send-button:hover:not(:disabled) {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
        }

        .send-button:disabled {
            background: #e5e7eb;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .stop-button {
            background: #ef4444 !important;
        }

        .stop-button:hover:not(:disabled) {
            background: #dc2626 !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        }

        /* 滚动条样式 */
        .messages-container::-webkit-scrollbar,
        .panel-content::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track,
        .panel-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .messages-container::-webkit-scrollbar-thumb,
        .panel-content::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover,
        .panel-content::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(8px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .left-panel {
                width: 480px;
            }

            .middle-panel {
                width: 340px;
            }
        }

        @media (max-width: 768px) {
            .workspace-content {
                flex-direction: column;
            }

            .left-panel, .middle-panel {
                width: 100%;
                height: 200px;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .right-panel {
                min-width: unset;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="chat-workspace">
            <!-- 顶部工具栏 -->
            <div class="workspace-header">
                <div class="header-left">
                    <button class="back-button" @click="goBack" title="返回">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 12H5"></path>
                            <path d="M12 19L5 12L12 5"></path>
                        </svg>
                    </button>
                    <div class="workspace-title">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 12l2 2 4-4"></path>
                            <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                            <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                        </svg>
                        智能体工作台
                        <div class="agent-name-container">
                            <span v-if="!isEditingAgentName" class="workspace-subtitle" @click="startEditAgentName">
                                {{ currentAgent.name || '未知智能体' }}
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="edit-icon">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                            </span>
                            <input v-if="isEditingAgentName" 
                                   v-model="editingAgentName" 
                                   @blur="saveAgentName" 
                                   @keyup.enter="saveAgentName"
                                   @keyup.escape="cancelEditAgentName"
                                   class="agent-name-input" 
                                   ref="agentNameInput"
                                   maxlength="50">
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="user-card" @click="showUserMenu">
                        <div class="user-avatar">{{ currentUser.charAt(0).toUpperCase() }}</div>
                        <div class="user-info">
                            <div class="user-name">{{ currentUser }}</div>
                            <div class="user-role">管理员</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="workspace-content">
                <!-- 左侧面板 - 系统提示词 -->
                <div class="left-panel">
                    <system-prompt-component
                        :agent-id="agentId"
                        @prompt-change="onPromptChange"
                        ref="systemPrompt"
                    />
                </div>

                <!-- 中间面板 - 流程配置 -->
                <div class="middle-panel">
                    <model-settings-component
                        :agent-id="agentId"
                        @config-change="onConfigChange"
                        ref="processConfig"
                    />
                </div>

                <!-- 右侧面板 - 聊天对话 -->
                <div class="right-panel">
                    <chat-conversation-component
                        :current-agent="currentAgent"
                        :agent-id="agentId"
                        @message-sent="onMessageSent"
                        ref="chatConversation"
                    />
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 等待组件加载完成后再创建Vue应用
        const initializeApp = () => {
            createApp({
                components: {
                    SystemPromptComponent: ChatComponents.SystemPromptComponent,
                    ModelSettingsComponent: ChatComponents.ModelSettingsComponent,
                    ChatConversationComponent: ChatComponents.ChatConversationComponent
                },
            setup() {
                const currentAgent = ref({});
                const currentUser = ref('YYT');
                const agentId = ref(null);
                const systemPrompt = ref(null);
                const processConfig = ref(null);
                const chatConversation = ref(null);
                const isEditingAgentName = ref(false);
                const editingAgentName = ref('');
                const agentNameInput = ref(null);

                // 配置axios
                axios.defaults.withCredentials = true;
                axios.defaults.headers.common['Content-Type'] = 'application/json';

                // 检查登录状态
                const checkAuth = () => {
                    if (!AuthUtils.requireAuth()) {
                        return false;
                    }
                    currentUser.value = AuthUtils.getCurrentUser() || 'YYT';
                    return true;
                };

                // 获取URL参数中的智能体ID
                const getAgentIdFromUrl = () => {
                    const urlParams = new URLSearchParams(window.location.search);
                    return urlParams.get('agentId');
                };

                // 根据agentId获取智能体信息
                const loadAgentInfo = async (agentId) => {
                    if (!agentId) {
                        return null;
                    }

                    try {
                        const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-templates'), {});

                        if (response.data && response.data.success) {
                            const agents = response.data.data || [];
                            const agent = agents.find(a => a.agentId === agentId);

                            if (agent) {
                                return agent;
                            } else {
                                throw new Error('未找到对应的智能体');
                            }
                        } else {
                            throw new Error(response.data?.errMessage || '获取智能体信息失败');
                        }
                    } catch (error) {
                        console.error('加载智能体信息失败:', error);
                        ElMessage.error('加载智能体信息失败: ' + (error.response?.data?.errMessage || error.message));
                        return null;
                    }
                };

                // 返回首页
                const goBack = () => {
                    window.location.href = 'index.html';
                };

                // 显示用户菜单
                const showUserMenu = () => {
                    ElMessageBox.confirm('确定要退出登录吗？', '用户菜单', {
                        confirmButtonText: '退出登录',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        logout();
                    }).catch(() => {
                        // 取消操作
                    });
                };

                // 退出登录
                const logout = () => {
                    AuthUtils.logout();
                    window.location.href = 'login.html';
                };

                // 系统提示词变化
                const onPromptChange = (prompt) => {
                    console.log('系统提示词变化:', prompt);
                };

                // 流程配置变化
                const onConfigChange = (config) => {
                    console.log('流程配置变化:', config);
                };

                // 消息发送
                const onMessageSent = async (message) => {
                    console.log('发送消息:', message);

                    try {
                        await handleStreamResponse(message);
                    } catch (error) {
                        console.error('发送消息失败:', error);
                        ElMessage.error('发送消息失败，请重试');
                    }
                };

                // 开始编辑智能体名称
                const startEditAgentName = () => {
                    isEditingAgentName.value = true;
                    editingAgentName.value = currentAgent.value.name || '未知智能体';
                    // 使用nextTick确保DOM更新后再聚焦
                    Vue.nextTick(() => {
                        if (agentNameInput.value) {
                            agentNameInput.value.focus();
                            agentNameInput.value.select();
                        }
                    });
                };

                // 保存智能体名称
                const saveAgentName = async () => {
                    const newName = editingAgentName.value.trim();
                    if (newName === '') {
                        ElMessage.warning('智能体名称不能为空');
                        return;
                    }

                    if (newName === currentAgent.value.name) {
                        // 名称没有变化，直接取消编辑
                        cancelEditAgentName();
                        return;
                    }

                    try {
                        // 调用后端API更新智能体名称
                        const response = await axios.post(AuthUtils.buildApiUrl('/playground/v1/agent-template/update'), {
                            agentId: agentId.value,
                            name: newName
                        });

                        if (response.data && response.data.success) {
                            // 更新成功，更新本地状态
                            currentAgent.value.name = newName;
                            isEditingAgentName.value = false;
                            ElMessage.success('智能体名称已更新');
                        } else {
                            throw new Error(response.data?.errMessage || '更新失败');
                        }
                    } catch (error) {
                        console.error('保存智能体名称失败:', error);
                        ElMessage.error('保存失败: ' + (error.response?.data?.errMessage || error.message));
                    }
                };

                // 取消编辑智能体名称
                const cancelEditAgentName = () => {
                    isEditingAgentName.value = false;
                    editingAgentName.value = '';
                };

                // 流式聊天响应
                const handleStreamResponse = async (userMessage) => {
                    const chatComponent = chatConversation.value;
                    if (!chatComponent) return;

                    let aiMessage = null;

                    try {
                        // 设置为接收状态
                        chatComponent.isReceiving = true;

                        // 创建一个占位的AI消息
                        aiMessage = {
                            id: Date.now() + 1,
                            role: 'assistant',
                            content: '',
                            timestamp: new Date(),
                            isStreaming: true
                        };
                        chatComponent.messages.push(aiMessage);

                        // 洞察信息将通过服务器端的事件流动态添加
                        // 不再使用固定的示例数据

                        chatComponent.scrollToBottom();

                        // 发送聊天请求
                        const response = await fetch(`${AuthUtils.getBaseUrl()}/playground/v1/chat/stream`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'text/event-stream'
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                agentId: agentId.value,
                                message: userMessage.content,
                                images: userMessage.images || []
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        // 读取流式响应
                        const reader = response.body.getReader();
                        chatComponent.currentReader = reader;
                        const decoder = new TextDecoder();
                        let buffer = '';
                        let currentEvent = null;

                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');

                            // 保留最后一行（可能不完整）
                            buffer = lines.pop() || '';

                            for (const line of lines) {
                                const trimmedLine = line.trim();

                                if (trimmedLine.startsWith('event:')) {
                                    currentEvent = trimmedLine.substring(6).trim();
                                } else if (trimmedLine.startsWith('data:')) {
                                    const data = trimmedLine.substring(5).trim();

                                    if (currentEvent === 'start') {
                                        try {
                                            const eventData = JSON.parse(data);
                                            if (eventData.content === 'started') {
                                                console.log('聊天开始');
                                            }
                                        } catch (e) {
                                            console.error('解析start事件数据失败:', e);
                                        }
                                    } else if (currentEvent === 'progress') {
                                        if (data && data !== '') {
                                            try {
                                                const progressData = JSON.parse(data);
                                                const messageIndex = chatComponent.messages.findIndex(m => m.id === aiMessage.id);

                                                if (messageIndex !== -1) {
                                                    addInsightToMessage(
                                                        chatComponent,
                                                        messageIndex,
                                                        'progress',
                                                        progressData.content || '正在处理...',
                                                        progressData.title || '处理步骤',
                                                        '1.0s',
                                                        { msgId: progressData.msgId, questionMsgId: progressData.questionMsgId }
                                                    );
                                                }
                                            } catch (e) {
                                                console.error('解析progress事件数据失败:', e);
                                            }
                                        }
                                    } else if (currentEvent === 'insight') {
                                        if (data && data !== '') {
                                            try {
                                                const insightData = JSON.parse(data);
                                                const messageIndex = chatComponent.messages.findIndex(m => m.id === aiMessage.id);

                                                if (messageIndex !== -1) {
                                                    addInsightToMessage(
                                                        chatComponent,
                                                        messageIndex,
                                                        insightData.type || 'general',
                                                        insightData.content || '正在处理...',
                                                        insightData.title || '处理步骤',
                                                        insightData.duration || '0.0s',
                                                        insightData.metadata || {}
                                                    );
                                                }
                                            } catch (e) {
                                                console.error('解析insight事件数据失败:', e);
                                            }
                                        }
                                    } else if (currentEvent === 'content') {
                                        if (data && data !== '') {
                                            try {
                                                const contentData = JSON.parse(data);
                                                const messageIndex = chatComponent.messages.findIndex(m => m.id === aiMessage.id);

                                                if (messageIndex !== -1) {
                                                    const currentMessage = chatComponent.messages[messageIndex];
                                                    const newContent = currentMessage.content + (contentData.content || '');
                                                    chatComponent.messages[messageIndex] = {
                                                        ...currentMessage,
                                                        content: newContent
                                                    };
                                                    chatComponent.$nextTick(() => {
                                                        chatComponent.scrollToBottom();
                                                    });
                                                }
                                            } catch (e) {
                                                console.error('解析content事件数据失败:', e);
                                                // 如果JSON解析失败，可能是原始字符串，直接使用
                                                const messageIndex = chatComponent.messages.findIndex(m => m.id === aiMessage.id);
                                                if (messageIndex !== -1) {
                                                    const currentMessage = chatComponent.messages[messageIndex];
                                                    const newContent = currentMessage.content + data;
                                                    chatComponent.messages[messageIndex] = {
                                                        ...currentMessage,
                                                        content: newContent
                                                    };
                                                    chatComponent.$nextTick(() => {
                                                        chatComponent.scrollToBottom();
                                                    });
                                                }
                                            }
                                        }
                                    } else if (currentEvent === 'end') {
                                        try {
                                            const eventData = JSON.parse(data);
                                            if (eventData.content === 'completed') {
                                                console.log('聊天完成');
                                            }
                                        } catch (e) {
                                            console.error('解析end事件数据失败:', e);
                                        }
                                    } else if (currentEvent === 'error') {
                                        try {
                                            const eventData = JSON.parse(data);
                                            throw new Error(eventData.content || '服务器错误');
                                        } catch (e) {
                                            if (e.message.includes('JSON')) {
                                                throw new Error(data);
                                            }
                                            throw e;
                                        }
                                    }
                                } else if (trimmedLine === '') {
                                    currentEvent = null;
                                }
                            }
                        }

                        // 标记流式消息完成
                        const messageIndex = chatComponent.messages.findIndex(m => m.id === aiMessage.id);
                        if (messageIndex !== -1) {
                            const finalMessage = chatComponent.messages[messageIndex];
                            chatComponent.messages[messageIndex] = {
                                ...finalMessage,
                                isStreaming: false
                            };
                        }

                        chatComponent.scrollToBottom();

                    } catch (error) {
                        console.error('流式聊天失败:', error);
                        ElMessage.error('聊天失败: ' + error.message);

                        // 移除失败的消息
                        if (aiMessage && chatComponent) {
                            const messageIndex = chatComponent.messages.findIndex(m => m.id === aiMessage.id);
                            if (messageIndex !== -1) {
                                chatComponent.messages.splice(messageIndex, 1);
                            }
                        }
                    } finally {
                        // 无论成功还是失败，都重置接收状态
                        if (chatComponent) {
                            chatComponent.isReceiving = false;
                            chatComponent.currentReader = null;
                        }
                    }
                };

                // 添加洞察到消息
                const addInsightToMessage = (chatComponent, messageIndex, type, content, title, duration, metadata) => {
                    const currentMessage = chatComponent.messages[messageIndex];

                    if (!currentMessage.insights) {
                        currentMessage.insights = [];
                    }

                    const insightId = `${currentMessage.id}-${type}-${Date.now()}`;
                    const newInsight = {
                        id: insightId,
                        type: type,
                        content: content,
                        title: title || '洞察信息',
                        duration: duration || '1.0s',
                        metadata: metadata || {}
                    };

                    const newInsights = [...currentMessage.insights, newInsight];
                    const updatedMessage = {
                        ...currentMessage,
                        insights: newInsights
                    };

                    chatComponent.messages.splice(messageIndex, 1, updatedMessage);
                };

                // 注意：洞察信息现在通过服务器端的 'insight' 事件流动态添加
                // 不再使用固定的示例数据

                // 组件挂载时初始化
                onMounted(async () => {
                    if (!checkAuth()) {
                        return;
                    }

                    const id = getAgentIdFromUrl();
                    if (id) {
                        agentId.value = id;
                        const agent = await loadAgentInfo(id);
                        if (agent) {
                            currentAgent.value = agent;
                        } else {
                            ElMessage.error('未找到智能体信息，正在关闭窗口...');
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        }
                    } else {
                        ElMessage.error('缺少智能体ID参数，正在关闭窗口...');
                        setTimeout(() => {
                            window.close();
                        }, 2000);
                    }
                });

                return {
                    currentAgent,
                    currentUser,
                    agentId,
                    systemPrompt,
                    processConfig,
                    chatConversation,
                    isEditingAgentName,
                    editingAgentName,
                    agentNameInput,
                    goBack,
                    showUserMenu,
                    onPromptChange,
                    onConfigChange,
                    onMessageSent,
                    startEditAgentName,
                    saveAgentName,
                    cancelEditAgentName
                };
            }
            }).use(ElementPlus).mount('#app');
        };

        // 等待组件加载完成后再初始化应用
        if (window.ChatComponents) {
            // 组件已经加载完成
            initializeApp();
        } else {
            // 等待组件加载完成事件
            window.addEventListener('chat-components-loaded', () => {
                initializeApp();
            });
        }
    </script>
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <pattern>
                    <pattern>
                        {
                        "time": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "level": "%level",
                        "pid": "${PID:-}",
                        "thread": "%thread",
                        "requestId": "%X{requestId}",
                        "traceId": "%X{EagleEye-TraceID}",
                        "logger": "%logger{64}",
                        "file": "%file",
                        "method": "%method",
                        "line": "%line",
                        "message": "%message",
                        "stack_trace":"%exception{5}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>

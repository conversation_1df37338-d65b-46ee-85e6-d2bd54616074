package com.dangbei.molili.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "oss sts数据")
public class OssStsCo extends DTO {

    @Schema(description = "OSS接入地址")
    private String ossEndpoint;

    @Schema(description = "访问密钥")
    private String accessKeyId;

    @Schema(description = "访问密钥ID")
    private String accessKeySecret;

    @Schema(description = "安全令牌")
    private String securityToken;

    @Schema(description = "地域ID")
    private String regionId;

    @Schema(description = "目标存储空间")
    private String bucket;

    @Schema(description = "上传路径")
    private String uploadPath;

    @Schema(description = "过期时间")
    private Long expiration;

    @Schema(description = "域名")
    private String domainName;

    @Schema(description = "文件名")
    private String fileName;
}

package com.dangbei.molili.infrastructure.memory.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 检索记忆请求
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class MemorySearchRequest {

    @J<PERSON>NField(name = "collection_name")
    private String collectionName;
    @JSONField(name = "query")
    private String query;
    @JSONField(name = "filter")
    private Filter filter;
    @JSONField(name = "limit")
    private Integer limit;

    @Data
    @Accessors(chain = true)
    public static class Filter {
        @J<PERSON>NField(name = "user_id")
        private List<String> userId;
        @J<PERSON><PERSON>ield(name = "assistant_id")
        private List<String> assistantId;
        @JSONField(name = "primary_key")
        private List<String> primaryKey;
        @J<PERSON>NField(name = "start_time")
        private Long startTime;
        @J<PERSON><PERSON>ield(name = "end_time")
        private Long endTime;
        @J<PERSON><PERSON>ield(name = "memory_type")
        private List<String> memoryType;
        @JSO<PERSON>ield(name = "group_id")
        private List<String> groupId;
    }

}

/*
 * Copyright 2012-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dangbei.molili.infrastructure.common.base;

import com.dangbei.molili.domain.common.base.BaseEntity;

import java.util.Collection;
import java.util.List;

/**
 * 转换器(领域对象 <-> 数据库对象)
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
public interface BaseConvertor<DataObject extends BaseDO, Entity extends BaseEntity> {

    /**
     * DO 转 Entity
     * @param dataObject DO对象
     * @return 领域对象
     */
    Entity toEntity(DataObject dataObject);

    /**
     * DO 转 Entity
     * @param dataObjectList DO对象
     * @return 领域对象
     */
    List<Entity> toEntityList(Collection<DataObject> dataObjectList);

    /**
     * Entity 转 DO
     * @param entity 领域对象
     * @return DO对象
     */
    DataObject toDO(Entity entity);

    /**
     * Entity 转 DO
     * @param entityList 领域对象
     * @return DO对象
     */
    List<DataObject> toDOList(Collection<Entity> entityList);

}

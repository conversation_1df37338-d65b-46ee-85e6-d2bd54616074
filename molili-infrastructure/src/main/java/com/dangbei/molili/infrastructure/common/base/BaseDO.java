package com.dangbei.molili.infrastructure.common.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据库对象基类
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Data
public abstract class BaseDO<I extends Serializable> implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 创建人
     */
    @TableField(value = "create_person", fill = FieldFill.INSERT)
    private String createPerson;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_person", fill = FieldFill.INSERT_UPDATE)
    private String updatePerson;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除字段(0：未删除；主键：已删除)
     */
    @TableField(value = "is_deleted")
    private I isDeleted;

    /**
     * 用于实现延迟加载(懒加载)
     */
    @TableField(exist = false)
    private Boolean loadFlag = true;

    /**
     * 获取主键
     * @return {@link I }
     */
    public abstract I getPrimaryId();

    /**
     * 设置主键
     * @param id id
     */
    public abstract void setPrimaryId(I id);
}

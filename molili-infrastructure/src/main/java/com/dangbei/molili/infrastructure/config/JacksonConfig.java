package com.dangbei.molili.infrastructure.config;

import com.alibaba.cola.common.constant.DateTimeConstant;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Jackson 配置
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Configuration
public class JacksonConfig {

    /**
     * LocalDate 和 LocalDateTime 时间类型反序列化
     * @return {@link Jackson2ObjectMapperBuilderCustomizer }
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            // Body LocalDate 时间类型反序列化
            builder.deserializerByType(LocalDate.class, new LocalDateDeserializer(DateTimeConstant.DATE_FORMATTER));
            // Body LocalDateTime 时间类型反序列化
            builder.deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeConstant.DATE_TIME_FORMATTER));
        };
    }
}

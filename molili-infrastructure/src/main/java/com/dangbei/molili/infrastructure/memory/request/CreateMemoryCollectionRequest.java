package com.dangbei.molili.infrastructure.memory.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 创建记忆库请求
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class CreateMemoryCollectionRequest {

    @JSONField(name = "CollectionName")
    private String collectionName;
    @JSONField(name = "Description")
    private String description;
    @JSONField(name = "BuiltinEventTypes")
    private List<String> builtinEventTypes;
    @JSONField(name = "BuiltinEntityTypes")
    private List<String> builtinEntityTypes;
    @JSONField(name = "CustomEventTypeSchemas")
    private List<EventTypeSchema> customEventTypeSchemas;
    @JSONField(name = "CustomEntityTypeSchemas")
    private List<EntityTypeSchema> customEntityTypeSchemas;

    @Data
    @Accessors(chain = true)
    public static class EventTypeSchema {
        @JSONField(name = "EventType")
        private String eventType;
        @JSONField(name = "Description")
        private String description;
        @JSONField(name = "Properties")
        private List<EventProperty> properties;
        @JSONField(name = "Version")
        private String version;
        @JSONField(name = "ValidationExpression")
        private String validationExpression;
    }

    @Data
    @Accessors(chain = true)
    public static class EventProperty {
        @JSONField(name = "PropertyName")
        private String propertyName;
        @JSONField(name = "PropertyValueType")
        private String propertyValueType;
        @JSONField(name = "Description")
        private String description;
    }

    @Data
    @Accessors(chain = true)
    public static class EntityTypeSchema {
        @JSONField(name = "EntityType")
        private String entityType;
        @JSONField(name = "AssociatedEventTypes")
        private List<String> associatedEventTypes;
        @JSONField(name = "Description")
        private String description;
        @JSONField(name = "Properties")
        private List<EntityProperty> properties;
        @JSONField(name = "Version")
        private String version;
    }

    @Data
    @Accessors(chain = true)
    public static class EntityProperty {
        @JSONField(name = "PropertyName")
        private String propertyName;
        @JSONField(name = "PropertyValueType")
        private String propertyValueType;
        @JSONField(name = "Description")
        private String description;
        @JSONField(name = "AggregateExpression")
        private AggregateExpression aggregateExpression;
        @JSONField(name = "UseProvided")
        private boolean useProvided;
        @JSONField(name = "IsPrimaryKey")
        private boolean isPrimaryKey;
    }

    @Data
    @Accessors(chain = true)
    public static class AggregateExpression {
        @JSONField(name = "Op")
        private String op;
        @JSONField(name = "EventType")
        private String eventType;
        @JSONField(name = "EventPropertyName")
        private String eventPropertyName;
    }

}

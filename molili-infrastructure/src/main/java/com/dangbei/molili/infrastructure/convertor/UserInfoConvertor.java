package com.dangbei.molili.infrastructure.convertor;

import com.dangbei.molili.domain.entity.UserInfoEntity;
import com.dangbei.molili.infrastructure.common.base.BaseConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.UserInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 转换器 Entity <---> DO
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserInfoConvertor extends BaseConvertor<UserInfoDO, UserInfoEntity> {

}

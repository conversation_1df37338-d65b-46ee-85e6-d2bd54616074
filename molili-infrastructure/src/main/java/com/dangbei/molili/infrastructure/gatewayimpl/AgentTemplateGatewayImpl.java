package com.dangbei.molili.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.molili.domain.entity.AgentTemplateEntity;
import com.dangbei.molili.domain.gateway.AgentTemplateGateway;
import com.dangbei.molili.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.molili.infrastructure.convertor.AgentTemplateConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.AgentTemplateDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.AgentTemplateMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AgentTemplate 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Component
public class AgentTemplateGatewayImpl extends BaseGatewayImpl<Long, AgentTemplateEntity, AgentTemplateDO, AgentTemplateMapper, AgentTemplateConvertor> implements AgentTemplateGateway {
    @Override
    public List<AgentTemplateEntity> findAll() {
        return convertor.toEntityList(baseMapper.selectList(null));
    }

    @Override
    public AgentTemplateEntity findByAgentId(String agentId) {
        LambdaQueryWrapper<AgentTemplateDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AgentTemplateDO::getAgentId, agentId);
        return convertor.toEntity(baseMapper.selectOne(wrapper));
    }
}

package com.dangbei.molili.infrastructure.memory.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 记忆库详情查询响应
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class GetMemoryCollectionResponse {

    @JSONField(name = "ResponseMetadata")
    private ResponseMetadata responseMetadata;

    @JSONField(name = "Result")
    private Result result;

    @Data
    public static class ResponseMetadata {

        @JSONField(name = "Region")
        private String region;

        @JSONField(name = "RequestId")
        private String requestId;

        @JSONField(name = "Service")
        private String service;

        @JSONField(name = "Version")
        private String version;
    }

    @Data
    public static class Result {

        @JSONField(name = "created_at")
        private String createdAt;

        @JSONField(name = "created_by")
        private String createdBy;

        @JSONField(name = "desc_msg")
        private String descMsg;

        @JSONField(name = "name")
        private String name;

        @JSONField(name = "pipeline_config")
        private Map<String, Object> pipelineConfig;

        @JSONField(name = "llm_extract_config")
        private LlmExtractConfig llmExtractConfig;

        @JSONField(name = "project")
        private String project;

        @JSONField(name = "resource_id")
        private String resourceId;

        @JSONField(name = "updated_at")
        private String updatedAt;
    }

    @Data
    public static class LlmExtractConfig {
        @JSONField(name = "builtin_entity_types")
        private List<String> builtinEntityTypes;

        @JSONField(name = "builtin_event_types")
        private List<String> builtinEventTypes;

        @JSONField(name = "custom_entity_type_schemas")
        private List<EntityTypeSchema> customEntityTypeSchemas;

        @JSONField(name = "custom_event_type_schemas")
        private List<EventTypeSchema> customEventTypeSchemas;
    }

    @Data
    public static class EntityTypeSchema {
        @JSONField(name = "associated_event_types")
        private List<String> associatedEventTypes;

        @JSONField(name = "description")
        private String description;

        @JSONField(name = "entity_type")
        private String entityType;

        @JSONField(name = "properties")
        private List<EntityProperty> properties;

        @JSONField(name = "role")
        private String role;

        @JSONField(name = "version")
        private String version;
    }

    @Data
    public static class EntityProperty {
        @JSONField(name = "aggregate_expression")
        private AggregateExpression aggregateExpression;

        @JSONField(name = "description")
        private String description;

        @JSONField(name = "is_primary_key")
        private Boolean primaryKey;

        @JSONField(name = "property_name")
        private String propertyName;

        @JSONField(name = "property_value_type")
        private String propertyValueType;

        @JSONField(name = "use_provided")
        private Boolean useProvided;
    }

    @Data
    public static class AggregateExpression {
        @JSONField(name = "event_property_name")
        private String eventPropertyName;

        @JSONField(name = "event_type")
        private String eventType;

        @JSONField(name = "op")
        private String op;
    }

    @Data
    public static class EventTypeSchema {
        @JSONField(name = "description")
        private String description;

        @JSONField(name = "event_type")
        private String eventType;

        @JSONField(name = "has_multi_event")
        private Boolean hasMultiEvent;

        @JSONField(name = "original_message_preserve_strategy")
        private String originalMessagePreserveStrategy;

        @JSONField(name = "properties")
        private List<EventProperty> properties;

        @JSONField(name = "searchable")
        private Boolean searchable;

        @JSONField(name = "validation_expression")
        private String validationExpression;

        @JSONField(name = "version")
        private String version;
    }

    @Data
    public static class EventProperty {
        @JSONField(name = "description")
        private String description;

        @JSONField(name = "property_name")
        private String propertyName;

        @JSONField(name = "property_value_type")
        private String propertyValueType;
    }

}

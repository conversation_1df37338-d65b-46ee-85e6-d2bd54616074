package com.dangbei.molili.infrastructure.memory.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 添加记忆响应
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class MemoryAddResponse {
    private Integer code;
    private String message;
    private Object data;
    @JSONField(name = "request_id")
    private String requestId;

}

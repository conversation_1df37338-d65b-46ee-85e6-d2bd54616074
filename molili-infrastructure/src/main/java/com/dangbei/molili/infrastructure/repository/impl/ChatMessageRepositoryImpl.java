package com.dangbei.molili.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.ChatMessageDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.ChatMessageMapper;
import com.dangbei.molili.infrastructure.repository.ChatMessageRepository;
import org.springframework.stereotype.Repository;

/**
 * ChatMessage 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@Repository
public class ChatMessageRepositoryImpl extends ServiceImpl<ChatMessageMapper, ChatMessageDO> implements ChatMessageRepository {

}

package com.dangbei.molili.infrastructure.config;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 雪花id生成器配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-06-19
 */
@Component
public class SnowflakeConfig {

    /**
     * 默认返回单例的Snowflake对象，集群情况下大概率不会出现分布式ID重复的情况（数据中心ID依赖于本地网卡MAC地址）
     * @return snowflake对象
     */
    @Bean
    public Snowflake snowflake() {
        return IdUtil.getSnowflake();
    }

}

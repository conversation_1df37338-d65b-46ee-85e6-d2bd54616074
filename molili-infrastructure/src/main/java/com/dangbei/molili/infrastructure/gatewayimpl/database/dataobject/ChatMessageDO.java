package com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.dangbei.molili.client.dto.ChatMsgExt;
import com.dangbei.molili.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ChatMessage DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ai_chat_message", autoResultMap = true)
public class ChatMessageDO extends BaseDO<Long> {

    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "智能体ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "会话ID")
    @TableField(value = "conversation_id")
    private String conversationId;

    @Schema(description = "消息ID")
    @TableField(value = "msg_id")
    private String msgId;

    @Schema(description = "对话ID")
    @TableField(value = "chat_id")
    private String chatId;

    @Schema(description = "角色")
    @TableField(value = "role")
    private String role;

    @Schema(description = "用户提问")
    @TableField(value = "question")
    private String question;

    @Schema(description = "AI回答回答")
    @TableField(value = "content")
    private String content;

    @Schema(description = "扩展信息")
    @TableField(value = "ext", typeHandler = Fastjson2TypeHandler.class)
    private ChatMsgExt ext;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}

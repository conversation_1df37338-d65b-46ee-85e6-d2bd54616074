package com.dangbei.molili.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Data
@Component
@ConfigurationProperties(prefix = "oss")
public class OssProperties {

    /**
     * OSS接入地址
     */
    private String ossEndpoint;

    /**
     * 域名
     */
    private String domainName;

    /**
     * STS接入地址
     */
    private String endpoint;

    /**
     * 访问密钥
     */
    private String accessKeyId;

    /**
     * 访问密钥
     */
    private String accessKeySecret;

    /**
     * 角色ARN
     */
    private String roleArn;

    /**
     * 自定义角色会话名称
     */
    private String roleSessionName;

    /**
     * 临时访问凭证权限
     */
    private String policy;

    /**
     * 地域ID
     */
    private String regionId;

    /**
     * 目标存储空间
     */
    private String bucket;

    /**
     * 访问凭证的有效时间 秒  范围15分钟～1个小时
     */
    private Long durationSeconds;

    /**
     * 缓存时间
     */
    private Long cacheSeconds;

    /**
     * 预签名 url 过期时间
     * 默认 1 小时
     */
    private Long preSignExpiration = 3600 * 1000L;
}

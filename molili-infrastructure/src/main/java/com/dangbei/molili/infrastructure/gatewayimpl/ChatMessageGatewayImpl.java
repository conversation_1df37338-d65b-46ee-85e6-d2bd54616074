package com.dangbei.molili.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.molili.domain.entity.ChatMessageEntity;
import com.dangbei.molili.domain.gateway.ChatMessageGateway;
import com.dangbei.molili.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.molili.infrastructure.convertor.ChatMessageConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.ChatMessageDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.ChatMessageMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ChatMessage 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@Component
public class ChatMessageGatewayImpl extends BaseGatewayImpl<Long, ChatMessageEntity, ChatMessageDO, ChatMessageMapper, ChatMessageConvertor> implements ChatMessageGateway {

    @Override
    public List<ChatMessageEntity> findHistoryMessages(String userId, String agentId, String beforeId, Integer limit) {
        LambdaQueryWrapper<ChatMessageDO> wrapper = Wrappers.lambdaQuery();

        // 基础查询条件
        wrapper.eq(ChatMessageDO::getUserId, userId)
               .eq(ChatMessageDO::getAgentId, agentId);

        // 如果指定了beforeId，则查询创建时间早于该消息的记录
        if (beforeId != null && !"0".equals(beforeId)) {
            // 先查找beforeId对应的消息的创建时间
            ChatMessageDO beforeMessage = baseMapper.selectOne(
                Wrappers.<ChatMessageDO>lambdaQuery()
                    .eq(ChatMessageDO::getMsgId, beforeId)
                    .select(ChatMessageDO::getCreateTime)
            );

            if (beforeMessage != null) {
                wrapper.lt(ChatMessageDO::getCreateTime, beforeMessage.getCreateTime());
            }
        }

        // 按创建时间降序排序，最新的在前面
        wrapper.orderByDesc(ChatMessageDO::getCreateTime);

        // 设置查询限制
        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<ChatMessageDO> doList = baseMapper.selectList(wrapper);
        return convertor.toEntityList(doList);
    }

    @Override
    public void clearHistoryMessages(String userId, String agentId) {
        LambdaUpdateWrapper<ChatMessageDO> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(ChatMessageDO::getUserId, userId);
        wrapper.eq(ChatMessageDO::getAgentId, agentId);
        baseMapper.delete(wrapper);
    }

}

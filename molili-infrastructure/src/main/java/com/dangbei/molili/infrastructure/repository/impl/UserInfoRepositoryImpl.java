package com.dangbei.molili.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.UserInfoDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.UserInfoMapper;
import com.dangbei.molili.infrastructure.repository.UserInfoRepository;
import org.springframework.stereotype.Repository;

/**
 * UserInfo 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Repository
public class UserInfoRepositoryImpl extends ServiceImpl<UserInfoMapper, UserInfoDO> implements UserInfoRepository {

}

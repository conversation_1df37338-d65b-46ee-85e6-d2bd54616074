package com.dangbei.molili.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.AgentTemplateDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.AgentTemplateMapper;
import com.dangbei.molili.infrastructure.repository.AgentTemplateRepository;
import org.springframework.stereotype.Repository;

/**
 * AgentTemplate 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Repository
public class AgentTemplateRepositoryImpl extends ServiceImpl<AgentTemplateMapper, AgentTemplateDO> implements AgentTemplateRepository {

}

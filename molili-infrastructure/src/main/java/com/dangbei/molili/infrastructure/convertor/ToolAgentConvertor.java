package com.dangbei.molili.infrastructure.convertor;

import com.dangbei.molili.domain.entity.ToolAgentEntity;
import com.dangbei.molili.infrastructure.common.base.BaseConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.ToolAgentDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 转换器 Entity <---> DO
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ToolAgentConvertor extends BaseConvertor<ToolAgentDO, ToolAgentEntity> {

}

package com.dangbei.molili.infrastructure.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.comm.SignVersion;
import com.dangbei.molili.infrastructure.config.properties.OssProperties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2025-03-11 16:01
 **/
@Configuration
public class OssConfig {

    @Resource
    private OssProperties ossProperties;

    @Bean
    public OSS ossClient() {
        var defaultCredentialProvider =
            CredentialsProviderFactory.newDefaultCredentialProvider(ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());

        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);

        return OSSClientBuilder.create()
            .endpoint(ossProperties.getOssEndpoint())
            .credentialsProvider(defaultCredentialProvider)
            .clientConfiguration(clientBuilderConfiguration)
            .region(ossProperties.getRegionId())
            .build();
    }
}

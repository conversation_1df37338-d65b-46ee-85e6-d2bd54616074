package com.dangbei.molili.infrastructure.common.util;

import com.alibaba.cola.common.util.BeanCopyUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.PageSingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 分页工具类
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@UtilityClass
public class PageUtil {

    /**
     * 将 mybatis plus 分页对象转换为 cola 分页对象
     * @param page     mybatis plus 分页对象
     * @param function 映射器
     * @return {@link PageResponse }<{@link target }>
     */
    public static <target, source> PageResponse<target> convert(Page<source> page, Function<source, target> function) {
        return PageResponse.of(page.getRecords().stream().map(function).collect(Collectors.toList()), page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 将 mybatis plus 分页对象转换为 cola 分页对象
     * @param page  mybatis plus 分页对象
     * @param clazz 类对象
     * @return {@link PageResponse }<{@link target }>
     */
    public static <target, source> PageResponse<target> convert(Page<source> page, Class<target> clazz) {
        List<target> targetList = BeanCopyUtil.copyList(page.getRecords(), clazz);
        return PageResponse.of(targetList, page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 将 mybatis plus 分页对象转换为 cola 分页对象
     * @param page     mybatis plus 分页对象
     * @param supplier supplier
     * @return {@link PageResponse }<{@link target }>
     */
    public static <target, source> PageResponse<target> convert(Page<source> page, Supplier<target> supplier) {
        List<target> targetList = BeanCopyUtil.copyList(page.getRecords(), supplier);
        return PageResponse.of(targetList, page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 转换 page 里面的实体
     * @param page     分页对象
     * @param function 映射器
     * @return {@link Page }<{@link target }>
     */
    public static <target, source> Page<target> convertPage(Page<source> page, Function<source, target> function) {
        List<target> targetList = page.getRecords().stream().map(function).collect(Collectors.toList());
        Page<target> targetPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        targetPage.setRecords(targetList);
        return targetPage;
    }

    /**
     * 转换 page 里面的实体
     * @param page  分页对象
     * @param clazz 类对象
     * @return {@link Page }<{@link target }>
     */
    public static <target, source> Page<target> convertPage(Page<source> page, Class<target> clazz) {
        List<target> targetList = BeanCopyUtil.copyList(page.getRecords(), clazz);
        Page<target> targetPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        targetPage.setRecords(targetList);
        return targetPage;
    }

    /**
     * 转换 page 里面的实体
     * @param page     分页对象
     * @param supplier supplier
     * @return {@link Page }<{@link target }>
     */
    public static <target, source> Page<target> convertPage(Page<source> page, Supplier<target> supplier) {
        List<target> targetList = BeanCopyUtil.copyList(page.getRecords(), supplier);
        Page<target> targetPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        targetPage.setRecords(targetList);
        return targetPage;
    }

    /**
     * 将 mybatis plus 分页对象转换为 cola 分页对象
     * @param page     mybatis plus 分页对象
     * @param function 映射器
     * @return {@link PageSingleResponse }<{@link target }>
     */
    public static <target, source> PageSingleResponse<target> convertToPageSingleResponse(Page<source> page, Function<source, target> function) {
        return PageSingleResponse.of(page.getRecords().stream().map(function).collect(Collectors.toList()), page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 将 mybatis plus 分页对象转换为 cola 分页对象
     * @param page  mybatis plus 分页对象
     * @param clazz 类对象
     * @return {@link PageSingleResponse }<{@link target }>
     */
    public static <target, source> PageSingleResponse<target> convertToPageSingleResponse(Page<source> page, Class<target> clazz) {
        List<target> targetList = BeanCopyUtil.copyList(page.getRecords(), clazz);
        return PageSingleResponse.of(targetList, page.getTotal(), page.getSize(), page.getCurrent());
    }

    /**
     * 将 mybatis plus 分页对象转换为 cola 分页对象
     * @param page     mybatis plus 分页对象
     * @param supplier supplier
     * @return {@link PageSingleResponse }<{@link target }>
     */
    public static <target, source> PageSingleResponse<target> convertToPageSingleResponse(Page<source> page, Supplier<target> supplier) {
        List<target> targetList = BeanCopyUtil.copyList(page.getRecords(), supplier);
        return PageSingleResponse.of(targetList, page.getTotal(), page.getSize(), page.getCurrent());
    }
}

package com.dangbei.molili.infrastructure.memory.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 记忆库详情查询请求
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class GetMemoryCollectionRequest {

    @JSONField(name = "CollectionName")
    private String collectionName;

}

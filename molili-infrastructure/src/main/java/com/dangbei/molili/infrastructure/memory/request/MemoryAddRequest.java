package com.dangbei.molili.infrastructure.memory.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 添加记忆请求
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class MemoryAddRequest {
    @JSONField(name = "collection_name")
    private String collectionName;
    @JSONField(name = "session_id")
    private String sessionId;
    @JSONField(name = "messages")
    private List<Message> messages;
    @JSONField(name = "metadata")
    private Metadata metadata;
    @JSONField(name = "entities")
    private Entities entities;

    @Data
    @Accessors(chain = true)
    public static class Message {
        @JSONField(name = "role")
        private String role;
        @JSONField(name = "content")
        private String content;
        @JSONField(name = "role_id")
        private String roleId;
        @JSONField(name = "role_name")
        private String roleName;
        @JSONField(name = "time")
        private Long time;
    }

    @Data
    @Accessors(chain = true)
    public static class Metadata {
        @JSONField(name = "default_user_id")
        private String defaultUserId;
        @JSONField(name = "default_user_name")
        private String defaultUserName;
        @JSONField(name = "default_assistant_id")
        private String defaultAssistantId;
        @JSONField(name = "default_assistant_name")
        private String defaultAssistantName;
        @JSONField(name = "time")
        private Long time;
        @JSONField(name = "group_id")
        private String groupId;
    }

    @Data
    @Accessors(chain = true)
    public static class Entities {
        @JSONField(name = "entity_type")
        private String entityType;
        @JSONField(name = "entity_scope")
        private List<Map<String, Object>> entityScopes;
    }

}

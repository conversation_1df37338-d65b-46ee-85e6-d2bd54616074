package com.dangbei.molili.infrastructure.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 记忆库配置
 * <AUTHOR> href="mailto:<EMAIL>">yinyanta<PERSON>@dangbei.com</a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Data
@Component
@ConfigurationProperties(prefix = "volc-memory")
public class VolcMemoryProperties {

    public String host = "api-knowledgebase.mlp.cn-beijing.volces.com";

    public String appKey = "AKLTMzEyNWYwYjZkYzI0NGQxZThkYWExODJkZmE5OTFlNjE";

    public String secretKey = "TlRWbFl6QmpZall3WTJJNE5HSTNNbUZrT0Rrek9UQXdObVpoT0RReFpEZw==";

    public String collectionName = "MOLILI_MEMORY_TEST";

}

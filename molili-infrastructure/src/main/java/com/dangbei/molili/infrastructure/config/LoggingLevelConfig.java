package com.dangbei.molili.infrastructure.config;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.YamlMapFactoryBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;

@Configuration(proxyBeanMethods = false)
@Slf4j
public class LoggingLevelConfig {
    @Value("${spring.cloud.nacos.config.group}")
    private String group;

    @Bean
    ApplicationListener<ApplicationReadyEvent> loggingLevelConfigNotify(NacosConfigManager nacosConfigManager,
                                                                        ObjectProvider<LoggingSystem> loggingSystemProvider,
                                                                        Environment env) {
        AtomicReference<String> dataId = new AtomicReference<>();
        Binder.get(env).bind("spring.config.import", Bindable.listOf(String.class))
            .ifBound(imports -> dataId.set(imports.stream()
                .filter(s -> s.startsWith("nacos:"))
                .map(s -> s.substring("nacos:".length()))
                .map(s -> s.substring(0, s.indexOf("?")))
                .findFirst()
                .orElse("")));

        return e -> {
            try {
                nacosConfigManager.getConfigService().addListener(dataId.get(), group, new Listener() {
                    @Override
                    public Executor getExecutor() {
                        return Executors.newSingleThreadExecutor();
                    }

                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        YamlMapFactoryBean factoryBean = new YamlMapFactoryBean();
                        factoryBean.setResources(new ByteArrayResource(configInfo.getBytes(StandardCharsets.UTF_8)));
                        var config = factoryBean.getObject();
                        if (config == null) {
                            log.warn("[监听Nacos Config ] Content 为空");
                            return;
                        }
                        var loggingMap = config.get("logging");
                        if (loggingMap instanceof Map<?, ?> loggingConfigs) {
                            Object levels = loggingConfigs.get("level");
                            if (levels instanceof Map<?, ?> levelConfigs) {
                                levelConfigs.forEach((packageInfo, level) -> {
                                    loggingSystemProvider.getIfAvailable(() -> LoggingSystem.get(Thread.currentThread().getContextClassLoader()))
                                        .setLogLevel(String.valueOf(packageInfo), LogLevel.valueOf(String.valueOf(level).toUpperCase()));
                                });
                            }
                        }
                    }
                });
            } catch (NacosException ex) {
                log.error("[add nacos config listener error]", ex);
            }
        };
    }

}

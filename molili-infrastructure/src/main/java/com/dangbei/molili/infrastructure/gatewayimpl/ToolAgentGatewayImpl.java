package com.dangbei.molili.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.molili.domain.entity.ToolAgentEntity;
import com.dangbei.molili.domain.gateway.ToolAgentGateway;
import com.dangbei.molili.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.molili.infrastructure.convertor.ToolAgentConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.ToolAgentDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.ToolAgentMapper;
import org.springframework.stereotype.Component;

/**
 * ToolAgent 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@Component
public class ToolAgentGatewayImpl extends BaseGatewayImpl<Long, ToolAgentEntity, ToolAgentDO, ToolAgentMapper, ToolAgentConvertor> implements ToolAgentGateway {

    @Override
    public ToolAgentEntity findByAgentId(String agentId) {
        LambdaQueryWrapper<ToolAgentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ToolAgentDO::getAgentId, agentId);
        return convertor.toEntity(baseMapper.selectOne(wrapper));
    }

}

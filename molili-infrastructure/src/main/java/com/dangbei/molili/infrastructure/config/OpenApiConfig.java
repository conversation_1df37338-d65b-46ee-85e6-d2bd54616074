package com.dangbei.molili.infrastructure.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenApi 配置
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022-03-22
 */
@Configuration
public class OpenApiConfig {

    /**
     * 从 application.properties 或 application.yml 中获取 servlet 的上下文路径
     */
    @Value("${server.servlet.context-path}")
    private String contextPath;

    /**
     * 创建并配置 OpenAPI 的 Bean
     * @return {@link OpenAPI }
     */
    @Bean
    public OpenAPI openApi() {
        return new OpenAPI()
            .addServersItem(new Server().url(StringUtils.defaultIfBlank(contextPath, "/")))
            .info(new Info()
                .title("系统后台 API")
                .description("这是一个系统后台API文档")
                .version("1.0.0")
                .license(new License().name("Apache 2.0").url("http://springdoc.org")));
    }


    /**
     * 自定义对默认 OpenAPI 描述和组的操作
     * @return {@link OperationCustomizer }
     */
    @Bean
    public OperationCustomizer operationCustomizer() {
        return (operation, handlerMethod) -> {
            operation.setOperationId(
                String.format(
                    "%s%s",
                    StringUtils.capitalize(handlerMethod.getMethod().getDeclaringClass().getSimpleName()),
                    StringUtils.uncapitalize(handlerMethod.getMethod().getName())));
            return operation;
        };
    }

}

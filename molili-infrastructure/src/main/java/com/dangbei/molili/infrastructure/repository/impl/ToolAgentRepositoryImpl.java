package com.dangbei.molili.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.ToolAgentDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.ToolAgentMapper;
import com.dangbei.molili.infrastructure.repository.ToolAgentRepository;
import org.springframework.stereotype.Repository;

/**
 * ToolAgent 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@Repository
public class ToolAgentRepositoryImpl extends ServiceImpl<ToolAgentMapper, ToolAgentDO> implements ToolAgentRepository {

}

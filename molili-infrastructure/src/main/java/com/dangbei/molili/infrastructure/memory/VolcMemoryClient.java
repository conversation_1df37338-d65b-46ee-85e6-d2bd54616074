package com.dangbei.molili.infrastructure.memory;

import com.alibaba.fastjson2.JSON;
import com.dangbei.molili.infrastructure.config.properties.VolcMemoryProperties;
import com.dangbei.molili.infrastructure.memory.request.CreateMemoryCollectionRequest;
import com.dangbei.molili.infrastructure.memory.request.GetMemoryCollectionRequest;
import com.dangbei.molili.infrastructure.memory.request.MemoryAddRequest;
import com.dangbei.molili.infrastructure.memory.request.MemorySearchRequest;
import com.dangbei.molili.infrastructure.memory.response.CreateMemoryCollectionResponse;
import com.dangbei.molili.infrastructure.memory.response.GetMemoryCollectionResponse;
import com.dangbei.molili.infrastructure.memory.response.MemoryAddResponse;
import com.dangbei.molili.infrastructure.memory.response.MemorySearchResponse;
import com.volcengine.auth.ISignerV4;
import com.volcengine.auth.impl.SignerV4Impl;
import com.volcengine.model.Credentials;
import com.volcengine.service.SignableRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.List;

/**
 * 火山记忆库客户端
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Slf4j
@Component
public class VolcMemoryClient {

    @Resource
    private VolcMemoryProperties volcMemoryProperties;

    /**
     * 创建记忆库
     * @param request 创建记忆库请求
     * @return 创建记忆库响应
     * @throws Exception 签名异常
     */
    public CreateMemoryCollectionResponse createCollection(CreateMemoryCollectionRequest request) throws Exception {
        String requestJson = JSON.toJSONString(request);
        String responseJson = doCreateCollection(requestJson);
        return JSON.parseObject(responseJson, CreateMemoryCollectionResponse.class);
    }

    /**
     * 查询记忆库信息
     * @param request 查询记忆库请求
     * @return 查询记忆库响应
     * @throws Exception 签名异常
     */
    public GetMemoryCollectionResponse getCollectionInfo(GetMemoryCollectionRequest request) throws Exception {
        String requestJson = JSON.toJSONString(request);
        String responseJson = doGetCollectionInfo(requestJson);
        return JSON.parseObject(responseJson, GetMemoryCollectionResponse.class);
    }

    /**
     * 添加记忆
     * @param request 添加记忆请求
     * @return 添加记忆响应
     * @throws Exception 签名异常
     */
    public MemoryAddResponse addMemory(MemoryAddRequest request) throws Exception {
        String requestJson = JSON.toJSONString(request);
        String responseJson = doAddMessages(requestJson);
        return JSON.parseObject(responseJson, MemoryAddResponse.class);
    }

    /**
     * 搜索记忆
     * @param request 搜索记忆请求
     * @return 搜索记忆响应
     * @throws Exception 签名异常
     */
    public MemorySearchResponse searchMemory(MemorySearchRequest request) throws Exception {
        String requestJson = JSON.toJSONString(request);
        String responseJson = doSearchMemory(requestJson);
        return JSON.parseObject(responseJson, MemorySearchResponse.class);
    }

    /**
     * 创建记忆库
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    private String doCreateCollection(String requestJson) throws Exception {
        return executeRequest("/api/memory/collection/create", requestJson);
    }

    /**
     * 查询记忆库
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    private String doGetCollectionInfo(String requestJson) throws Exception {
        return executeRequest("/api/memory/collection/info", requestJson);
    }

    /**
     * 上传对话
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    private String doAddMessages(String requestJson) throws Exception {
        return executeRequest("/api/memory/messages/add", requestJson);
    }

    /**
     * 检索记忆
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    private String doSearchMemory(String requestJson) throws Exception {
        return executeRequest("/api/memory/search", requestJson);
    }

    /**
     * 执行HTTP请求
     * @param path        请求路径
     * @param requestJson 请求参数
     * @return 响应结果
     * @throws Exception 签名异常
     */
    private String executeRequest(String path, String requestJson) throws Exception {
        try {
            String host = volcMemoryProperties.getHost();
            String ak = volcMemoryProperties.getAppKey();
            String sk = volcMemoryProperties.getSecretKey();

            SignableRequest signableRequest = prepareRequest(host, path, "POST", null, requestJson, ak, sk);
            URI uri = new URIBuilder()
                .setScheme("https")
                .setHost(host)
                .setPath(path)
                .build();

            HttpPost httpPost = new HttpPost(uri);
            httpPost.setConfig(signableRequest.getConfig());
            httpPost.setEntity(signableRequest.getEntity());
            for (Header header : signableRequest.getAllHeaders()) {
                httpPost.setHeader(header.getName(), header.getValue());
            }

            HttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            log.info("调用火山记忆库：\n请求:{}\n响应：{}", requestJson, responseBody);
            return responseBody;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 准备请求
     * @param host   主机
     * @param path   路径
     * @param method 方法
     * @param params 参数
     * @param body   请求体
     * @param ak     访问密钥
     * @param sk     秘密密钥
     * @return 可签名请求
     * @throws Exception 签名异常
     */
    private SignableRequest prepareRequest(String host, String path, String method, List<NameValuePair> params, String body, String ak, String sk) throws Exception {
        SignableRequest request = new SignableRequest();
        request.setMethod(method);
        request.setHeader("Accept", "application/json");
        request.setHeader("Content-Type", "application/json");
        request.setHeader("Host", host);
        request.setEntity(new StringEntity(body, "utf-8"));

        URIBuilder builder = request.getUriBuilder();
        builder.setScheme("https");
        builder.setHost(host);
        builder.setPath(path);
        if (params != null) {
            builder.setParameters(params);
        }

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(120000).setConnectTimeout(12000).build();
        request.setConfig(requestConfig);

        Credentials credentials = new Credentials("cn-north-1", "air");
        credentials.setAccessKeyID(ak);
        credentials.setSecretAccessKey(sk);

        // 签名
        ISignerV4 ISigner = new SignerV4Impl();
        ISigner.sign(request, credentials);

        return request;
    }


}

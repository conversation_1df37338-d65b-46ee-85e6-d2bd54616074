package com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.molili.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UserAgent DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ai_user_agent")
public class UserAgentDO extends BaseDO<Long> {

    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "智能体唯一ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "用户-交互设定")
    @TableField(value = "interaction_profile")
    private String interactionProfile;

    @Schema(description = "用户-特质人格设定")
    @TableField(value = "trait_profile")
    private String traitProfile;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}

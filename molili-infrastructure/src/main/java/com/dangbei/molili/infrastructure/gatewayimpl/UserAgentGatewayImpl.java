package com.dangbei.molili.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.molili.domain.entity.AgentTemplateEntity;
import com.dangbei.molili.domain.entity.UserAgentEntity;
import com.dangbei.molili.domain.gateway.UserAgentGateway;
import com.dangbei.molili.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.molili.infrastructure.convertor.UserAgentConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.UserAgentDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.UserAgentMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * UserAgent 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Component
public class UserAgentGatewayImpl extends BaseGatewayImpl<Long, UserAgentEntity, UserAgentDO, UserAgentMapper, UserAgentConvertor> implements UserAgentGateway {
    @Override
    public List<UserAgentEntity> listByUser(String userId) {
        LambdaQueryWrapper<UserAgentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAgentDO::getUserId, userId);
        wrapper.orderByDesc(UserAgentDO::getCreateTime);
        return convertor.toEntityList(list(wrapper));
    }

    @Override
    public UserAgentEntity getOrCreate(String userId, AgentTemplateEntity agentTemplate) {
        LambdaQueryWrapper<UserAgentDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAgentDO::getUserId, userId);
        wrapper.eq(UserAgentDO::getAgentId, agentTemplate.getAgentId());
        wrapper.orderByDesc(UserAgentDO::getCreateTime);
        UserAgentDO userAgentDO = getOne(wrapper);
        if (Objects.isNull(userAgentDO)) {
            UserAgentEntity newSave = new UserAgentEntity();
            newSave.setUserId(userId);
            newSave.setAgentId(agentTemplate.getAgentId());
            newSave.setName(agentTemplate.getName());
            newSave.setInteractionProfile(agentTemplate.getInteractionProfile());
            newSave.setTraitProfile(agentTemplate.getTraitProfile());
            insert(newSave);
            return newSave;
        }
        return convertor.toEntity(userAgentDO);
    }

}

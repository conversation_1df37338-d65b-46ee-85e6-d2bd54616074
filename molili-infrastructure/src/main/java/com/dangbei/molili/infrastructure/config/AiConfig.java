package com.dangbei.molili.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.ClientHttpRequestFactories;
import org.springframework.boot.web.client.ClientHttpRequestFactorySettings;
import org.springframework.boot.web.client.RestClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;

import java.nio.charset.StandardCharsets;
import java.time.Duration;


/**
 * SpringAI配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-15
 */
@Slf4j
@Configuration
public class AiConfig {

    @Bean
    public RestClientCustomizer restClientCustomizer() {
        return restClientBuilder -> restClientBuilder
            .requestInterceptor(logInterceptor())
            .requestFactory(ClientHttpRequestFactories.get(ClientHttpRequestFactorySettings.DEFAULTS
                .withConnectTimeout(Duration.ofSeconds(5))
                .withReadTimeout(Duration.ofSeconds(600))));
    }


    private ClientHttpRequestInterceptor logInterceptor() {
        return (request, body, execution) -> {
            // ✅ 打印请求元信息
            log.info("""

                🔍 请求 URL:{}
                🔍 请求 Header:{}
                📤 请求体:{}
                """, request.getURI(), request.getHeaders(), new String(body, StandardCharsets.UTF_8));
            return execution.execute(request, body);
        };
    }

}

package com.dangbei.molili.infrastructure.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Slf4j
@Configuration
@EnableAsync(proxyTargetClass = true)
public class AsyncConfig implements AsyncConfigurer {

    /**
     * 初始化线程池
     * @return {@link ThreadPoolTaskExecutor }
     */
    @Override
    @Bean("chatAsyncExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new AsyncDecorator());
        // 核心线程数：线程池创建时候初始化的线程数
        executor.setCorePoolSize(600);
        // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(Integer.MAX_VALUE);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(0);
        // 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
        executor.setKeepAliveSeconds(1800);
        // 设置线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁，以确保应用最后能够被关闭，而不是阻塞住
        executor.setAwaitTerminationSeconds(300);
        // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        executor.setThreadNamePrefix("Chat-Async-Service-");
        // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
        // 1、AbortPolicy：直接抛出异常，默认策略；
        // 2、CallerRunsPolicy：用调用者所在的线程来执行任务；
        // 3、DiscardOldestPolicy：丢弃阻塞队列中靠最前的任务，并执行当前任务；
        // 4、DiscardPolicy：直接丢弃任务；
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }

    /**
     * 捕获 @Async 方法抛出的异常
     * @return {@link AsyncUncaughtExceptionHandler }
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (e, method, params) -> log.error("[chatAsyncExecutor任务执行异常] method: {}, params: {}", method, params, e);
    }

}

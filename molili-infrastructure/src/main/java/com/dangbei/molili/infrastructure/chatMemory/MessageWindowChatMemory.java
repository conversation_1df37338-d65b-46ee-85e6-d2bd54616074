package com.dangbei.molili.infrastructure.chatMemory;


import com.alibaba.cloud.ai.memory.redis.serializer.MessageDeserializer;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.Builder;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Redis implementation of ChatMemoryRepository
 *
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
public class MessageWindowChatMemory implements ChatMemory {

    public static final String DEFAULT_KEY_PREFIX = "spring_ai_alibaba_chat_memory:";
    private static final int DEFAULT_MAX_MESSAGES = 20;
    private static final int DEFAULT_MAX_TIME = 2592000;
    private final int maxMessages;
    private final int maxTime;
    private final ObjectMapper objectMapper;

    private MessageWindowChatMemory(int maxMessages, int maxTime) {
        this.objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Message.class, new MessageDeserializer());
        objectMapper.registerModule(module);
        this.maxMessages = maxMessages;
        this.maxTime = maxTime;
    }

    public List<Message> findByConversationId(String conversationId) {
        Assert.hasText(conversationId, "conversationId cannot be null or empty");
        String key = DEFAULT_KEY_PREFIX + conversationId;
        Object messageObject = RedisUtil.get(key);
        if (Objects.nonNull(messageObject)) {
            List<JSONObject> jsonObjectList = JSON.parseArray(String.valueOf(messageObject), JSONObject.class);
            return jsonObjectList.stream()
                .map(this::mapRow)
                .collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    public void saveAll(String conversationId, List<Message> messages) {
        Assert.hasText(conversationId, "conversationId cannot be null or empty");
        Assert.notNull(messages, "messages cannot be null");
        Assert.noNullElements(messages, "messages cannot contain null elements");

        List<JSONObject> jsonObjects = new ArrayList<>();
        for (Message message : messages) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("content", message.getText());
            jsonObject.put("type", message.getMessageType().name());
            jsonObjects.add(jsonObject);
        }

        String key = DEFAULT_KEY_PREFIX + conversationId;
        String messageListJson = JSON.toJSONString(jsonObjects);
        RedisUtil.set(key, messageListJson, maxTime);
    }

    public void deleteByConversationId(String conversationId) {
        Assert.hasText(conversationId, "conversationId cannot be null or empty");
        String key = DEFAULT_KEY_PREFIX + conversationId;
        RedisUtil.del(key);
    }

    @Override
    public void add(String conversationId, List<Message> messages) {
        Assert.hasText(conversationId, "conversationId cannot be null or empty");
        Assert.notNull(messages, "messages cannot be null");
        Assert.noNullElements(messages, "messages cannot contain null elements");

        List<Message> memoryMessages = this.findByConversationId(conversationId);
        List<Message> processedMessages = process(memoryMessages, messages);
        this.saveAll(conversationId, processedMessages);
    }

    @Override
    public List<Message> get(String conversationId) {
        Assert.hasText(conversationId, "conversationId cannot be null or empty");
        return this.findByConversationId(conversationId);
    }

    @Override
    public void clear(String conversationId) {
        Assert.hasText(conversationId, "conversationId cannot be null or empty");
        this.deleteByConversationId(conversationId);
    }

    private List<Message> process(List<Message> memoryMessages, List<Message> newMessages) {
        List<Message> processedMessages = new ArrayList<>();

        Set<Message> memoryMessagesSet = new HashSet<>(memoryMessages);
        boolean hasNewSystemMessage = newMessages.stream()
            .filter(SystemMessage.class::isInstance)
            .anyMatch(message -> !memoryMessagesSet.contains(message));

        memoryMessages.stream()
            .filter(message -> !(hasNewSystemMessage && message instanceof SystemMessage))
            .forEach(processedMessages::add);

        processedMessages.addAll(newMessages);

        if (processedMessages.size() <= this.maxMessages) {
            return processedMessages;
        }

        int messagesToRemove = processedMessages.size() - this.maxMessages;

        List<Message> trimmedMessages = new ArrayList<>();
        int removed = 0;
        for (Message message : processedMessages) {
            if (message instanceof SystemMessage || removed >= messagesToRemove) {
                trimmedMessages.add(message);
            } else {
                removed++;
            }
        }

        return trimmedMessages;
    }

    public static MessageWindowChatMemory.Builder builder() {
        return new MessageWindowChatMemory.Builder();
    }

    public static final class Builder {

        private int maxMessages = DEFAULT_MAX_MESSAGES;
        private int maxTime = DEFAULT_MAX_TIME;

        private Builder() {
        }

        public MessageWindowChatMemory.Builder maxMessages(int maxMessages) {
            this.maxMessages = maxMessages;
            return this;
        }

        public MessageWindowChatMemory.Builder maxTime(int maxTime) {
            this.maxTime = maxTime;
            return this;
        }

        public MessageWindowChatMemory build() {
            return new MessageWindowChatMemory(this.maxMessages, this.maxTime);
        }

    }

    private Message mapRow(JSONObject jsonObject) {
        var content = jsonObject.getString("content");
        var type = MessageType.valueOf(jsonObject.getString("type"));
        return switch (type) {
            case USER -> new UserMessage(content);
            case ASSISTANT -> new AssistantMessage(content);
            case SYSTEM -> new SystemMessage(content);
            // The content is always stored empty for ToolResponseMessages.
            // If we want to capture the actual content, we need to extend
            // AddBatchPreparedStatement to support it.
            case TOOL -> new ToolResponseMessage(List.of());
        };
    }
}


package com.dangbei.molili.infrastructure.memory.response;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 检索记忆响应
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
@Accessors(chain = true)
public class MemorySearchResponse {

    @JSONField(name = "code")
    private Integer code;
    @JSONField(name = "message")
    private String message;
    @JSONField(name = "data")
    private DataInfo data;
    @JSONField(name = "request_id")
    private String requestId;

    @Data
    public static class DataInfo {
        @JSONField(name = "collection_name")
        private String collectionName;
        @JSONField(name = "count")
        private Integer count;
        @JSONField(name = "result_list")
        private List<ResultItem> resultList;
        @JSONField(name = "token_usage")
        private Integer tokenUsage;
    }

    @Data
    public static class ResultItem {
        @JSONField(name = "id")
        private String id;
        @JSO<PERSON>ield(name = "score")
        private Float score;
        @JSONField(name = "memory_type")
        private String memoryType;
        @JSONField(name = "user_id")
        private List<String> userId;
        @JSONField(name = "assistant_id")
        private List<String> assistantId;
        @JSONField(name = "session_id")
        private String sessionId;
        @JSONField(name = "group_id")
        private String groupId;
        @JSONField(name = "time")
        private Long time;
        @JSONField(name = "status")
        private String status;
        @JSONField(name = "labels")
        private String labels;
        // TODO 这是个啥呢？
        @JSONField(name = "memory_info")
        private JSONObject memoryInfo;
    }

}

package com.dangbei.molili.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.UserAgentDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.UserAgentMapper;
import com.dangbei.molili.infrastructure.repository.UserAgentRepository;
import org.springframework.stereotype.Repository;

/**
 * UserAgent 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Repository
public class UserAgentRepositoryImpl extends ServiceImpl<UserAgentMapper, UserAgentDO> implements UserAgentRepository {

}

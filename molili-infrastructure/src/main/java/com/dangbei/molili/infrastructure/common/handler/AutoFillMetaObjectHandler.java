package com.dangbei.molili.infrastructure.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import com.dangbei.molili.infrastructure.common.base.BaseDO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 自动补充插入或更新时的值
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/05/29
 */
@Component
public class AutoFillMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时填充
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject instanceof BaseDO baseDo) {
            LocalDateTime now = LocalDateTime.now();
            if (Objects.isNull(baseDo.getCreateTime())) {
                baseDo.setCreateTime(now);
            }
            if (Objects.isNull(baseDo.getUpdateTime())) {
                baseDo.setUpdateTime(now);
            }
            // LoginUserInfo loginInfo = ContextUtil.getLoginInfo();
            // if (Objects.nonNull(loginInfo)) {
            //     if (StringUtils.isBlank(baseDo.getCreatePerson())) {
            //         baseDo.setCreatePerson(loginInfo.getUserName());
            //     }
            //     if (StringUtils.isBlank(baseDo.getUpdatePerson())) {
            //         baseDo.setUpdatePerson(loginInfo.getUserName());
            //     }
            // }
        }
    }

    /**
     * 更新时填充
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject instanceof BaseDO baseDo) {
            if (Objects.isNull(baseDo.getUpdateTime())) {
                baseDo.setUpdateTime(LocalDateTime.now());
            }
            // LoginUserInfo loginInfo = ContextUtil.getLoginInfo();
            // if (Objects.nonNull(loginInfo) && StringUtils.isBlank(baseDo.getUpdatePerson())) {
            //     baseDo.setUpdatePerson(loginInfo.getUserName());
            // }
        }
    }
}

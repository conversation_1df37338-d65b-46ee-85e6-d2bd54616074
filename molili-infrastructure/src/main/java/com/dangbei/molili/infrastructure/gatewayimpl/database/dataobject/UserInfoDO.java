package com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.molili.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * UserInfo DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ai_user_info")
public class UserInfoDO extends BaseDO<Long> {

    @Schema(description = "本地用户主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID，全局唯一")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "SteamID64，全局唯一")
    @TableField(value = "steam_id")
    private String steamId;

    @Schema(description = "用户昵称")
    @TableField(value = "username")
    private String username;

    @Schema(description = "头像链接")
    @TableField(value = "avatar_url")
    private String avatarUrl;

    @Schema(description = "用户邮箱")
    @TableField(value = "email")
    private String email;

    @Schema(description = "账户创建时间")
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    @Schema(description = "最后登录时间")
    @TableField(value = "last_login_at")
    private LocalDateTime lastLoginAt;

    @Schema(description = "是否封禁")
    @TableField(value = "banned")
    private Integer banned;

    @Schema(description = "封禁原因")
    @TableField(value = "ban_reason")
    private String banReason;

    @Schema(description = "扩展字段（如地区、语言等）")
    @TableField(value = "metadata")
    private String metadata;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}

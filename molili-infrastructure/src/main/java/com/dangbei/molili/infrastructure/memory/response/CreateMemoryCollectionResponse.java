package com.dangbei.molili.infrastructure.memory.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 创建记忆库响应
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-07
 */
@Data
public class CreateMemoryCollectionResponse {

    @JSONField(name = "ResponseMetadata")
    private ResponseMetadata responseMetadata;
    @JSONField(name = "Result")
    private Result result;

    @Data
    public static class ResponseMetadata {
        @JSONField(name = "Action")
        private String action;
        @JSONField(name = "Region")
        private String region;
        @JSONField(name = "RequestId")
        private String requestId;
        @JSONField(name = "Service")
        private String service;
        @JSONField(name = "Version")
        private String version;
    }

    @Data
    public static class Result {
        @JSONField(name = "resource_id")
        private String resourceId;
    }

}

package com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.molili.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AgentTemplate DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ai_agent_template")
public class AgentTemplateDO extends BaseDO<Long> {

    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "智能体唯一ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "自然属性设定（只读）")
    @TableField(value = "natural_attributes")
    private String naturalAttributes;

    @Schema(description = "气质人格设定（只读）")
    @TableField(value = "temperament_profile")
    private String temperamentProfile;

    @Schema(description = "交互设定（可变）")
    @TableField(value = "interaction_profile")
    private String interactionProfile;

    @Schema(description = "特质人格设定（可变）")
    @TableField(value = "trait_profile")
    private String traitProfile;

    @Schema(description = "系统提示词")
    @TableField(value = "system_prompt")
    private String systemPrompt;

    @Schema(description = "用户提示词")
    @TableField(value = "user_prompt")
    private String userPrompt;

    @Schema(description = "对话模型")
    @TableField(value = "chat_model")
    private String chatModel;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}

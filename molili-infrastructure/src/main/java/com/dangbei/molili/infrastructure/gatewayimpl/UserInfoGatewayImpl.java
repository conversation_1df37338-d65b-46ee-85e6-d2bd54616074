package com.dangbei.molili.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dangbei.molili.domain.entity.UserInfoEntity;
import com.dangbei.molili.domain.gateway.UserInfoGateway;
import com.dangbei.molili.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.molili.infrastructure.convertor.UserInfoConvertor;
import com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject.UserInfoDO;
import com.dangbei.molili.infrastructure.gatewayimpl.database.mapper.UserInfoMapper;
import org.springframework.stereotype.Component;

/**
 * UserInfo 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Component
public class UserInfoGatewayImpl extends BaseGatewayImpl<Long, UserInfoEntity, UserInfoDO, UserInfoMapper, UserInfoConvertor> implements UserInfoGateway {

    @Override
    public UserInfoEntity findByUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return null;
        }
        
        LambdaQueryWrapper<UserInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfoDO::getUserId, userId);
        
        UserInfoDO userInfoDO = getOne(queryWrapper);
        
        return convertor.toEntity(userInfoDO);
    }

}

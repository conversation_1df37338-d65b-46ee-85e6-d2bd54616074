package com.dangbei.molili.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.molili.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ToolAgent DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ai_tool_agent")
public class ToolAgentDO extends BaseDO<Long> {

    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "Agent唯一ID")
    @TableField(value = "agent_id")
    private String agentId;

    @Schema(description = "名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "系统提示词")
    @TableField(value = "system_prompt")
    private String systemPrompt;

    @Schema(description = "用户提示词")
    @TableField(value = "user_prompt")
    private String userPrompt;

    @Schema(description = "聊天模型")
    @TableField(value = "chat_model")
    private String chatModel;

    @Schema(description = "输出变量")
    @TableField(value = "output_variable")
    private String outputVariable;

    @Schema(description = "输出变量类型 1-字符串 2-JSON格式 3-MD格式")
    @TableField(value = "output_variable_type")
    private Integer outputVariableType;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}

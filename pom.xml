<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>molili</artifactId>
    <version>${revision}</version>
    <groupId>com.dangbei</groupId>
    <packaging>pom</packaging>
    <name>molili</name>
    <description>molili</description>

    <parent>
        <groupId>com.dangbei</groupId>
        <artifactId>db-parent</artifactId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>

        <!-- Spring AI -->
        <spring-ai.version>1.0.0</spring-ai.version>
        <!-- Spring AI Alibaba -->
        <spring-ai-alibaba.version>*******</spring-ai-alibaba.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--Project modules-->
            <dependency>
                <groupId>com.dangbei</groupId>
                <artifactId>molili-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dangbei</groupId>
                <artifactId>molili-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dangbei</groupId>
                <artifactId>molili-app</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dangbei</groupId>
                <artifactId>molili-domain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dangbei</groupId>
                <artifactId>molili-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dangbei</groupId>
                <artifactId>molili-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules End-->

            <dependency>
                <groupId>com.dangbei.framework</groupId>
                <artifactId>insight-signature-spring-boot3-starter</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>

            <!-- 火山引擎SDK -->
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>1.0.227</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.2</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>${spring-ai-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>molili-client</module>
        <module>molili-adapter</module>
        <module>molili-app</module>
        <module>molili-common</module>
        <module>molili-domain</module>
        <module>molili-infrastructure</module>
        <module>molili-starter</module>
    </modules>

    <build>
        <pluginManagement>

        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>

@startuml

!if %variable_exists("$THEME")
title 阿里巴巴 COLA 应用架构时序图 - $THEME theme
!else
title 阿里巴巴 COLA 应用架构时序图
!endif

autonumber
participant "主动适配器" #SkyBlue
participant "db-prototype-adapter" #LightGreen
participant "db-prototype-app" #LightGreen
participant "db-prototype-domain" #LightGreen
participant "db-prototype-infrastructure" #LightGreen
participant "被动驱动器" #Pink
participant "扩展点" #Khaki

== 场景一：HTTP更新数据请求 ==

"主动适配器" -> "db-prototype-adapter": 发送写请求报文
"db-prototype-adapter" -> "db-prototype-app": 适配器组装数据传输对象
"db-prototype-app" -> "db-prototype-app": CQRS 解析出命令参数
"db-prototype-app" -> "扩展点": 根据指令调用扩展功能（可选项）
"db-prototype-app" -> "db-prototype-domain": 调用领域层
"db-prototype-domain" -> "db-prototype-infrastructure": 通过防腐层执行数据写操作
"db-prototype-infrastructure" -> "被动驱动器": 调用底层组件进行写操作
"db-prototype-infrastructure" --> "db-prototype-app": 返回查询数据
"db-prototype-app" --> "db-prototype-adapter": 组装返回数据
"db-prototype-adapter" --> "主动适配器": 响应报文

== 场景二：HTTP查询数据请求 ==

"主动适配器" -> "db-prototype-adapter": 发送读请求报文
"db-prototype-adapter" -> "db-prototype-app": 适配器组装数据传输对象
"db-prototype-app" -> "db-prototype-app": CQRS 解析出查询参数
"db-prototype-app" -> "db-prototype-infrastructure": 执行数据读操作
"db-prototype-infrastructure" -> "被动驱动器": 调用底层组件进行读操作
"db-prototype-infrastructure" --> "db-prototype-app": 返回查询数据
"db-prototype-app" --> "db-prototype-adapter": 组装返回数据
"db-prototype-adapter" --> "主动适配器": 响应报文

== 场景三：MQ消息驱动/Job定时任务触发 ==

"主动适配器" -> "db-prototype-adapter": 监听事件触发
"db-prototype-adapter" -> "db-prototype-app": CQRS 分发
alt#Gold #LightBlue 领域调用
	"db-prototype-app" -> "db-prototype-domain": 调用领域层
	"db-prototype-domain" -> "db-prototype-infrastructure": 通过防腐层执行数据写操作
	"db-prototype-infrastructure" -> "被动驱动器": 调用底层组件进行写操作
	"db-prototype-infrastructure" --> "db-prototype-app": 返回更新结果
else #Pink 简单查询
	"db-prototype-app" -> "db-prototype-infrastructure": 执行数据读操作
	"db-prototype-infrastructure" -> "被动驱动器": 调用底层组件进行读操作
	"db-prototype-infrastructure" --> "db-prototype-app": 返回查询数据
end
"db-prototype-app" -> "db-prototype-app": 内部处理（ACK确认/Status状态）
"db-prototype-app" --> "db-prototype-adapter": 处理结果上报
"db-prototype-adapter" --> "主动适配器": 上报结果

@enduml

@startuml

!if %variable_exists("$THEME")
title 阿里巴巴 COLA 应用架构组件图 - $THEME theme
!else
title 阿里巴巴 COLA 应用架构组件图
!endif

[db-prototype-adapter] <<适配层>>
[db-prototype-app] <<应用层>>
[db-prototype-client] <<API层>>
[db-prototype-domain] <<领域层>>
[db-prototype-infrastructure] <<基础设施层>>
[db-prototype-start] <<启动入口>>

[db-prototype-start] -u-> [db-prototype-adapter]
[db-prototype-adapter] --> [db-prototype-app]: 入站适配，数据组装
[db-prototype-app] --> [db-prototype-client]: 实现接口
[db-prototype-app] --> [db-prototype-domain]: CQRS 增删改命令
[db-prototype-app] --> [db-prototype-infrastructure]: CQRS 查询视图
[db-prototype-infrastructure] -up-> [db-prototype-domain]: 依赖倒置

node "Web、Mobile、WAP" <<主动适配器>>
control "Job调度平台" <<主动适配器>>
queue "MQ消息队列" <<主动适配器>>

cloud "第三方接口" <<被动驱动器>>
database "MySQL" <<被动驱动器>>
database "Redis" <<被动驱动器>>
database "MQ" <<被动驱动器>>
database "Elasticsearch" <<被动驱动器>>
database "MongoDB" <<被动驱动器>>

[Web、Mobile、WAP] .d.> [db-prototype-adapter]: 前后端对接
[Job调度平台] <.d.> [db-prototype-adapter]: 任务调度
[db-prototype-adapter] <.u.> [MQ消息队列]: 消费消息

[db-prototype-infrastructure] .d.> MySQL: 读写数据
[db-prototype-infrastructure] .d.> Elasticsearch: 读写索引
[db-prototype-infrastructure] .d.> MongoDB: 读写数据
[db-prototype-infrastructure] .d.> MQ: 生产消息
[db-prototype-infrastructure] .d.> Redis: 读写缓存
[db-prototype-infrastructure] .d.> 第三方接口: 接口调用

skinparam component {
 	backgroundColor<<适配层>> LightGreen
    backgroundColor<<应用层>> LightGreen
    backgroundColor<<领域层>> LightGreen
    backgroundColor<<基础设施层>> LightGreen
    backgroundColor<<启动入口>> LightGreen
    backgroundColor<<API层>> Khaki
}

skinparam cloud {
    backgroundColor<<主动适配器>> SkyBlue
    backgroundColor<<被动驱动器>> Pink
}

skinparam database {
    backgroundColor<<主动适配器>> SkyBlue
    backgroundColor<<被动驱动器>> Pink
}

skinparam queue {
    backgroundColor<<主动适配器>> SkyBlue
    backgroundColor<<被动驱动器>> Pink
}

skinparam control {
    backgroundColor<<主动适配器>> SkyBlue
    backgroundColor<<被动驱动器>> Pink
}

skinparam node {
    backgroundColor<<主动适配器>> SkyBlue
    backgroundColor<<被动驱动器>> Pink
}

@enduml

# Java 8 LocalDateTime API 使用指南

## 初始化与获取信息

### 获取当前时间

```java
LocalDateTime now = LocalDateTime.now();
```

### 创建指定日期时间

```java
LocalDateTime localDateTime = LocalDateTime.of(2024, 6, 24, 12, 35, 41); // 2024年6月24日中午12点35分41秒
```

### 获取日期时间组成部分

```java
int year = localDateTime.getYear();  // 获取年份
int month = localDateTime.getMonthValue();  // 获取月份（1-12）
int dayOfMonth = localDateTime.getDayOfMonth();  // 获取月中的哪一天（1-31）
int dayOfYear = localDateTime.getDayOfYear(); // 获取一年中的第几天（1-366）
DayOfWeek dayOfWeek = localDateTime.getDayOfWeek();  // 获取星期几（DayOfWeek枚举类型）
int hour = localDateTime.getHour(); // 获取小时（0-23）
int minute = localDateTime.getMinute(); // 获取分钟（0-59）
int second = localDateTime.getSecond(); // 获取秒（0-59）
```

### 转换为日期/时间对象

```java
LocalDate localDate = localDateTime.toLocalDate();
LocalTime localTime = localDateTime.toLocalTime();
```

## 时间操作

### 时间加减

```java
LocalDateTime newLocalDateTime = localDateTime.plusYears(1); // 添加一年到localDateTime
LocalDateTime newLocalDateTime = localDateTime.minusMonths(2); // 将localDateTime减去两个月
// 其他方法：plusWeeks, minusDays, plusHours, minusMinutes, plusSeconds, minusNanos等
```

## 格式化与解析

### 格式化输出

```java
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
String formattedTime = localDateTime.format(formatter);
```

### 解析字符串为LocalDateTime

```java
LocalDateTime parsedLocalDateTime = LocalDateTime.parse(formattedTime, formatter);
```

## 比较与判断

### 比较日期时间

```java
boolean isAfter = localDateTime.isAfter(now); // 判断localDateTime是否在当前时间之后
boolean isBefore = localDateTime.isBefore(now); // 判断localDateTime是否在当前时间之前
boolean isEqual = localDateTime.isEqual(now); // 判断localDateTime是否等于当前时间
```

## 范围判断与重叠检测

### Hutool工具类

```java
boolean in = LocalDateTimeUtil.isIn(date, start, end); // date是否在start与end范围内，包含start与end
boolean in = LocalDateTimeUtil.isIn(date, start, end, false, false); // date是否在start与end范围内，不包含start，也不包含end

boolean overlap = LocalDateTimeUtil.isOverlap(); // 检查两个时间段是否有时间重叠(包括包含和相等)
```

## 计算间隔

### 计算时间差（获取两个时间的差，如果结束时间早于开始时间，结果为负）

```java
long between = localDateTime.until(now, ChronoUnit.DAYS); // 计算localDateTime与当前时间之间相隔的天数
// 或使用 hutool 工具类
long between = LocalDateTimeUtil.between(localDateTime, now, ChronoUnit.DAYS);  // 计算localDateTime与当前时间之间相隔的天数
```

## 修改日期时间

### 设置日期时间

```java
LocalDateTime newDateTime = localDateTime.withDayOfMonth(12); // 当前时间基础上，将localDateTime修改为当月的第12天
LocalDateTime newDateTime = localDateTime.withDayOfYear(10); // 当前时间基础上，将localDateTime修改为当年的第10天，即1月10日
LocalDateTime newDateTime = localDateTime.withYear(2025); // 当前时间基础上，将localDateTime的年份修改为2025
LocalDateTime newDateTime = localDateTime.withMonth(7); // 当前时间基础上，将localDateTime的月份修改为7月
```

### 设置特定时间

```java
LocalDateTime begin = localDateTime.with(LocalTime.MIN); // 获取localDateTime当天的开始时刻
LocalDateTime end = localDateTime.with(LocalTime.MAX); // 获取localDateTime当天的最后时刻
// 或使用 hutool 工具类
LocalDateTime begin = LocalDateTimeUtil.beginOfDay(localDateTime); // 获取localDateTime当天的开始时刻
LocalDateTime end = LocalDateTimeUtil.endOfDay(localDateTime); // 获取localDateTime当天的最后时刻
```

## 时区处理与转换

### `LocalDateTime`与`Instant`介绍

`LocalDateTime`表示某个具体时刻，包含年月日时分秒，它并不包含时区信息。
`Instant`代表一个时间戳，通常用于表示从1970年1月1日00:00:00 UTC开始经过的秒数和纳秒数。它是全球唯一的，不受时区影响。

### LocalDateTime与Date互转

```java
ZoneId zoneId = ZoneId.systemDefault(); // 获取Java虚拟机的默认时区，并不一定是"GMT"
ZoneId shanghaiZoneId = ZoneId.of("Asia/Shanghai"); // 上海时区，即"GMT+8:00"
```

```java
Date date = Date.from(localDateTime.atZone(zoneId).toInstant()); // LocalDateTime转Date

LocalDateTime time = date.toInstant().atZone(zoneId).toLocalDateTime(); // Date转LocalDateTime
// 或使用 hutool 工具类（时区为ZoneId.systemDefault()）
LocalDateTime time = DateUtil.toLocalDateTime(date); // Date转LocalDateTime
```

### LocalDateTime与Timestamp互转

```java
long timestamp = localDateTime.atZone(zoneId).toInstant().toEpochMilli(); // LocalDateTime转timestamp
// 或使用 hutool 工具类（时区为ZoneId.systemDefault()）
long timestamp = LocalDateTimeUtil.toEpochMilli(localDateTime); // LocalDateTime转timestamp

LocalDateTime dateTime = Instant.ofEpochMilli(timestamp).atZone(zoneId).toLocalDateTime(); // timestamp转LocalDateTime
// 或使用 hutool 工具类（时区为ZoneId.systemDefault()）
LocalDateTime dateTime = LocalDateTimeUtil.of(timestamp); // timestamp转LocalDateTime
```


